// swift-tools-version: 5.9
import PackageDescription

let package = Package(
  name: "OGNetworkLogger",
  platforms: [.iOS(.v15)],
  products: [
    .library(
      name: "OGNetworkLogger",
      targets: ["OGNetworkLogger"]
    )
  ],
  dependencies: [
    .package(path: "../OGExternalDependencies/OGDIService")
  ],
  targets: [
    .target(
      name: "OGNetworkLogger",
      dependencies: ["OGDIService"]
    ),
    .testTarget(
      name: "OGNetworkLoggerTests",
      dependencies: ["OGNetworkLogger"]
    )
  ]
)
