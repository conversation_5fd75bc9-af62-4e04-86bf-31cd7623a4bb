// swiftlint:disable type_name

import SwiftUI

// MARK: - OGNavigationBarBackButtonIconStyleKey

struct OGNavigationBarBackButtonIconStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGNavigationBarBackButtonIconStyle(style: DefaultOGNavigationBarBackButtonIconStyle())
}

extension EnvironmentValues {
  var styleOGNavigationBarBackButtonIcon: AnyOGNavigationBarBackButtonIconStyle {
    get { self[OGNavigationBarBackButtonIconStyleKey.self] }
    set { self[OGNavigationBarBackButtonIconStyleKey.self] = newValue }
  }
}
