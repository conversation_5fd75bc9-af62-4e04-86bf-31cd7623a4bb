// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGNavigationBarTop.
public struct OGNavigationBarTopStyleConfiguration {
  /// A view that describes the content of a the OGNavigationBarTop.
  public let content: OGNavigationBarTopStyleConfiguration.Content

  /// The type-erased content of a OGNavigationBarTop.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public var body: AnyView
  }
}
