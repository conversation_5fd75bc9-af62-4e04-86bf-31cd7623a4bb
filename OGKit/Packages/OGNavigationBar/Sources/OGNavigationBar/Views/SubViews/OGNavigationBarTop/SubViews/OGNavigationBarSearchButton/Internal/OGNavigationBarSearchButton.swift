// swiftlint:disable type_name

import OGCore
import OGDIService
import OGRouter
import OGSearch
import SwiftUI

// MARK: - OGNavigationBarSearchButton

struct OGNavigationBarSearchButton: View {
  @Environment(\.styleOGNavigationBarSearchButton) private var style
  @StateObject private var viewStore: Store = Self.make()
  init() {}

  var body: some View {
    style.makeBody(
      configuration: OGNavigationBarSearchButtonStyleConfiguration(
        content: OGNavigationBarSearchButtonStyleConfiguration.Content(
          content: content
        )
      )
    )
  }

  var content: some View {
    Button {
      Task {
        await viewStore.dispatch(.didTap)
      }
    } label: {
      OGNavigationBarSearchButtonIcon()
    }
  }
}

// MARK: - OGNavigationBarSearchButton_Previews

struct OGNavigationBarSearchButton_Previews: PreviewProvider {
  static var previews: some View {
    OGNavigationBarSearchButton()
  }
}
