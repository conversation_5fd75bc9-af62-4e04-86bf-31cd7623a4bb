// swift-tools-version: 5.9
import PackageDescription

let package = Package(
  name: "OGAirshipKit",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGAirshipKit",
      targets: ["OGAirshipKit"]
    ),
    .library(
      name: "OGAirshipKitTestsUtils",
      targets: ["OGAirshipKitTestsUtils"]
    )
  ],
  dependencies: [
    .package(url: "https://github.com/urbanairship/ios-library", exact: "17.7.3"),
    .package(path: "../OGCore"),
    .package(path: "../OGExternalDependencies/OGDIService"),
    .package(path: "../OGDomainStore"),
    .package(path: "../OGViewStore"),
    .package(path: "../OGSecret"),
    .package(path: "../OGFeatureKit/Packages/OGFeatureAdapter"),
    .package(path: "../OGRouter"),
    .package(path: "../OGDeepLinkHandler"),
    .package(path: "../OGFeatureKit/Packages/OGTenantKit"),
    .package(path: "../OGFeatureKit/Packages/OGTenantCore")
  ],
  targets: [
    .target(
      name: "OGAirshipKit",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGDomainStore",
        "OGSecret",
        "OGFeatureAdapter",
        "OGRouter",
        "OGDeepLinkHandler",
        "OGViewStore",
        "OGTenantKit",
        .product(name: "AirshipCore", package: "ios-library"),
        .product(name: "AirshipAutomation", package: "ios-library"),
        .product(name: "AirshipMessageCenter", package: "ios-library")
      ]
    ),
    .target(
      name: "OGAirshipKitTestsUtils",
      dependencies: [
        "OGAirshipKit",
        .product(name: "OGCoreTestsUtils", package: "OGCore"),
        .product(name: "OGDomainStoreTestsUtils", package: "OGDomainStore"),
        .product(name: "OGRouterTestsUtils", package: "OGRouter"),
        .product(name: "OGTenantCoreTestsUtils", package: "OGTenantCore"),
        .product(name: "OGTenantKitTestsUtils", package: "OGTenantKit")
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGAirshipKitTests",
      dependencies: ["OGAirshipKitTestsUtils"]
    )
  ]
)
