import SwiftUI

/// A type that applies custom appearance to
/// all OGNavigationBarSearchFields within a view hierarchy.
public protocol OGNavigationBarSearchFieldStyle {
  /// A view that represents the body of a OGNavigationBarSearchField.
  associatedtype Body: View

  /// The properties of a OGNavigationBarSearchField.
  typealias Configuration = OGNavigationBarSearchFieldStyleConfiguration

  /// Creates a view that represents the body of a OGNavigationBarSearchField.
  ///
  /// The system calls this method for each OGNavigationBarSearchField instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGNavigationBarSearchFieldStyle``.
  /// ```swift
  /// struct MyCustomOGNavigationBarSearchFieldStyle: OGNavigationBarSearchFieldStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGNavigationBarSearchFieldConfiguration`` of the  OGNavigationBarSearchField.
  /// - Returns: A view that represents a OGNavigationBarSearchField.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
