import OGDIService

public final class OGUserAgentContainer: OGDISharedContainer {
  public static var shared: OGUserAgentContainer = .init()
  public var manager: OGDIContainerManager = .init()

  public var userAgent: OGDIService<OGUserAgentProvidable> {
    self {
      OGUserAgent()
    }.cached
  }

  public var dynamicValuesProvider: OGDIService<OGUserAgentDynamicValuesProvidable> {
    self {
      OGUserAgentDynamicValuesProvider()
    }.cached
  }
}
