// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
  name: "OGUserAgent",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    // Products define the executables and libraries a package produces, making them visible to other packages.
    .library(
      name: "OGUserAgent",
      targets: ["OGUserAgent"]
    )
  ],
  dependencies: [
    .package(
      path: "../OGCore"
    ),

    .package(
      path: "../OGFeatureKit/Packages/OGFeatureAdapter"
    ),

    .package(
      path: "../OGExternalDependencies/OGDIService"
    )

  ],
  targets: [
    // Targets are the basic building blocks of a package, defining a module or a test suite.
    // Targets can depend on other targets in this package and products from dependencies.
    .target(
      name: "OGUserAgent",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGFeatureAdapter"
      ]
    ),

    .testTarget(
      name: "OGUserAgentTests",
      dependencies: [
        "OGUserAgent"
      ]
    )
  ]
)
