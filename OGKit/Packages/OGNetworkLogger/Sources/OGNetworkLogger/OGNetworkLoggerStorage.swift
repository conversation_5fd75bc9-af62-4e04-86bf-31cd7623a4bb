import Combine
import OGDIService
import SwiftUI

// MARK: - OGNetworkLoggerStorageable

public protocol OGNetworkLoggerStorageable {
  var requestsPublisher: Published<[OGNetworkLoggerRequestModel]>.Publisher { get }
  var filteredRequestsPublisher: Published<[OGNetworkLoggerRequestModel]>.Publisher { get }

  func request(forId id: String) -> OGNetworkLoggerRequestModel?
  func saveRequest(request: OGNetworkLoggerRequestModel)
  func clearRequests()
}

// MARK: - OGNetworkLoggerStorage

public final class OGNetworkLoggerStorage: OGNetworkLoggerStorageable, ObservableObject {
  @OGInjected(\OGNetworkLoggerContainer.ignoreManager) private var ignoreManager

  private let accessQueue = DispatchQueue(label: "com.ognetworklogger.queue", attributes: .concurrent)

  public var requestsPublisher: Published<[OGNetworkLoggerRequestModel]>.Publisher { $requests }
  public var filteredRequestsPublisher: Published<[OGNetworkLoggerRequestModel]>.Publisher { $filteredRequests }

  @Published private var requests: [OGNetworkLoggerRequestModel] = .init()
  @Published private var filteredRequests: [OGNetworkLoggerRequestModel] = .init()

  /// Saves a request to the storage.
  /// If the `.id` is already present in the storage the passed request will override the existing one.
  /// - Parameter request: Request to store
  public func saveRequest(request: OGNetworkLoggerRequestModel) {
    accessQueue.async(flags: .barrier) { [weak self] in
      guard let self else {
        return
      }
      if let index = self.requests.firstIndex(where: { $0.id == request.id }) {
        self.requests[index] = request
      } else {
        self.requests.insert(request, at: 0)
      }
      self.filteredRequests = self.getFilteredRequests()
    }
  }

  /// Removes all stored requests
  public func clearRequests() {
    accessQueue.async(flags: .barrier) { [weak self] in
      self?.requests.removeAll()
    }
  }

  public func request(forId id: String) -> OGNetworkLoggerRequestModel? {
    getFilteredRequests().first(where: { $0.id == id })
  }

  private func getFilteredRequests() -> [OGNetworkLoggerRequestModel] {
    var localRequests = [OGNetworkLoggerRequestModel]()
    localRequests = requests
    return filterRequestsIfNeeded(localRequests)
  }

  private func filterRequestsIfNeeded(_ requests: [OGNetworkLoggerRequestModel]) -> [OGNetworkLoggerRequestModel] {
    guard case let Ignore.enabled(ignoreHandler) = ignoreManager.ignore else {
      return requests
    }
    return requests.filter { ignoreHandler($0) == false }
  }
}
