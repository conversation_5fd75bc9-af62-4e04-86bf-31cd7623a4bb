import AVFoundation
import OGMacros
import OGMock

// MARK: - MockCaptureDeviceProtocol

public protocol MockCaptureDeviceProtocol {
  func mockRequestPermission(mediaType: AVMediaType) async -> Bool
  func mockAuthorizationStatus(mediaType: AVMediaType) -> AVAuthorizationStatus
}

// MARK: - MockCaptureDevice

@OGMock
public class MockCaptureDevice: MockCaptureDeviceProtocol {
  public init() {}

  public func mockRequestPermission(mediaType: AVMediaType) async -> Bool {
    await mock.mockRequestPermission(mediaType: mediaType)
  }

  public func mockAuthorizationStatus(mediaType: AVMediaType) -> AVAuthorizationStatus {
    mock.mockAuthorizationStatus(mediaType: mediaType)
  }
}
