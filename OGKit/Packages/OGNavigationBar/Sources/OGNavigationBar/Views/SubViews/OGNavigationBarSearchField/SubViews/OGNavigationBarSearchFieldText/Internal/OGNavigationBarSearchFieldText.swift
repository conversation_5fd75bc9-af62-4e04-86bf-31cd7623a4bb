// swiftlint:disable type_name

import SwiftUI

// MARK: - OGNavigationBarSearchFieldText

struct OGNavigationBarSearchFieldText: View {
  @Environment(\.styleOGNavigationBarSearchFieldText) private var style

  init() {}

  var body: some View {
    style.makeBody(
      configuration: OGNavigationBarSearchFieldTextStyleConfiguration(
        content: OGNavigationBarSearchFieldTextStyleConfiguration.Content(
          content: content
        )
      )
    )
  }

  var content: some View {
    Text("What are you searching for?")
  }
}

// MARK: - OGNavigationBarSearchFieldText_Previews

struct OGNavigationBarSearchFieldText_Previews: PreviewProvider {
  static var previews: some View {
    OGNavigationBarSearchFieldText()
  }
}
