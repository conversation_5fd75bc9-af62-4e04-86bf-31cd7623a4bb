import SwiftUI

/// A type that applies custom appearance to
/// all OGNavigationBarSearchFieldTexts within a view hierarchy.
public protocol OGNavigationBarSearchFieldTextStyle {
  /// A view that represents the body of a OGNavigationBarSearchFieldText.
  associatedtype Body: View

  /// The properties of a OGNavigationBarSearchFieldText.
  typealias Configuration = OGNavigationBarSearchFieldTextStyleConfiguration

  /// Creates a view that represents the body of a OGNavigationBarSearchFieldText.
  ///
  /// The system calls this method for each OGNavigationBarSearchFieldText instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGNavigationBarSearchFieldTextStyle``.
  /// ```swift
  /// struct MyCustomOGNavigationBarSearchFieldTextStyle: OGNavigationBarSearchFieldTextStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGNavigationBarSearchFieldTextConfiguration`` of the  OGNavigationBarSearchFieldText.
  /// - Returns: A view that represents a OGNavigationBarSearchFieldText.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
