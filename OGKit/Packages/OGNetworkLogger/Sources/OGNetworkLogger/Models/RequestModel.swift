import Foundation

// MARK: - OGNetworkLoggerRequestModel

public final class OGNetworkLoggerRequestModel: Codable, Hashable {
  public var identifier: Int {
    hashValue
  }

  public let id: String
  public let url: String
  public let host: String?
  public let port: Int?
  public let scheme: String?
  public let date: Date
  public let method: String
  public let headers: [String: String]
  public var credentials: [String: String]
  public var cookies: String?
  public var httpBody: Data?
  public var code: Int
  public var responseHeaders: [String: String]?
  public var responseData: Data?
  public var errorClientDescription: String?
  public var duration: Double?
  public var isFinished: Bool
  public var timeout: TimeInterval
  public var didExceedTimeout: Bool = false

  public init(request: NSURLRequest, session: URLSession?) {
    self.id = UUID().uuidString
    self.url = request.url?.absoluteString ?? ""
    self.host = request.url?.host
    self.port = request.url?.port
    self.scheme = request.url?.scheme
    self.date = Date()
    self.method = request.httpMethod ?? "GET"
    self.credentials = [:]
    var headers = request.allHTTPHeaderFields ?? [:]
    self.httpBody = request.httpBody
    self.code = 0
    self.isFinished = false
    self.timeout = request.timeoutInterval
    // collect all HTTP Request headers except the "Cookie" header. Many request representations treat cookies with special parameters or structures. For cookie collection, refer to the bottom part of this method
    session?.configuration.httpAdditionalHeaders?
      .filter { $0.0 != AnyHashable("Cookie") }
      .forEach { element in
        guard let key = element.0 as? String, let value = element.1 as? String else { return }
        headers[key] = value
      }
    self.headers = headers

    // if the target server uses HTTP Basic Authentication, collect username and password
    if
      let credentialStorage = session?.configuration.urlCredentialStorage,
      let host,
      let port {
      let protectionSpace = URLProtectionSpace(
        host: host,
        port: port,
        protocol: scheme,
        realm: host,
        authenticationMethod: NSURLAuthenticationMethodHTTPBasic
      )

      if let credentials = credentialStorage.credentials(for: protectionSpace)?.values {
        for credential in credentials {
          guard let user = credential.user, let password = credential.password else { continue }
          self.credentials[user] = password
        }
      }
    }

    //  collect cookies associated with the target host
    //  TODO: Add the else branch.
    //  With the condition below, it is handled only the case where session.configuration.httpShouldSetCookies == true.
    // Some developers could opt to handle cookie manually using the "Cookie" header stored in httpAdditionalHeaders
    // and disabling the handling provided by URLSessionConfiguration (httpShouldSetCookies == false).
    // See: https://developer.apple.com/documentation/foundation/nsurlsessionconfiguration/1411589-httpshouldsetcookies?language=objc
    if let session, let url = request.url, session.configuration.httpShouldSetCookies {
      if
        let cookieStorage = session.configuration.httpCookieStorage,
        let cookies = cookieStorage.cookies(for: url), !cookies.isEmpty {
        self.cookies = cookies.reduce("") { $0 + "\($1.name)=\($1.value);" }
      }
    }
  }

  public func initResponse(response: URLResponse) {
    guard let responseHttp = response as? HTTPURLResponse else { return }
    code = responseHttp.statusCode
    responseHeaders = responseHttp.allHeaderFields as? [String: String]
    requestFinished()
  }

  public func requestFinished(withError error: Error) {
    errorClientDescription = error.localizedDescription
    requestFinished()
  }

  public func requestFinished() {
    duration = fabs(date.timeIntervalSinceNow) * 1_000 // Find elapsed time and convert to milliseconds
    isFinished = true
  }

  public var responseSize: String {
    guard let responseData else { return "--" }

    if responseData.count >= 1_024 { // if is at least 1 KB
      return String(format: "%.2f KB", Double(responseData.count) / 1_024.0)
    } else if responseData.count > (1_024 * 1_024) { // if is at least 1 MB
      return String(format: "%.2f MB", Double(responseData.count) / 1_024.0 * 1_024.0)
    } else {
      return "\(responseData.count) bytes"
    }
  }

  var curlRequest: String {
    var components = ["$ curl -v"]

    guard
      let _ = host
    else {
      return "$ curl command could not be created"
    }

    if method != "GET" {
      components.append("-X \(method)")
    }

    components += headers.map {
      let escapedValue = String(describing: $0.value).replacingOccurrences(of: "\"", with: "\\\"")
      return "-H \"\($0.key): \(escapedValue)\""
    }

    if let httpBodyData = httpBody, let httpBody = String(data: httpBodyData, encoding: .utf8) {
      // the following replacingOccurrences handles cases where httpBody already contains the escape \ character before the double quotation mark (") character
      var escapedBody = httpBody.replacingOccurrences(of: "\\\"", with: "\\\\\"") // \" -> \\\"
      // the following replacingOccurrences escapes the character double quotation mark (")
      escapedBody = escapedBody.replacingOccurrences(of: "\"", with: "\\\"") // " -> \"

      components.append("-d \"\(escapedBody)\"")
    }

    for credential in credentials {
      components.append("-u \(credential.0):\(credential.1)")
    }

    if let cookies {
      components.append("-b \"\(cookies[..<cookies.index(before: cookies.endIndex)])\"")
    }

    components.append("\"\(url)\"")

    return components.joined(separator: " \\\n\t")
  }

  public static func == (lhs: OGNetworkLoggerRequestModel, rhs: OGNetworkLoggerRequestModel) -> Bool {
    lhs.id == rhs.id && lhs.url == rhs.url && lhs.host == rhs.host && lhs.port == rhs.port && lhs.scheme == rhs.scheme && lhs.date == rhs.date && lhs.method == rhs.method && lhs.headers == rhs.headers && lhs.credentials == rhs.credentials && lhs.cookies == rhs.cookies && lhs.httpBody == rhs.httpBody && lhs.code == rhs.code && lhs.responseHeaders == rhs.responseHeaders && lhs.responseData == rhs.responseData && lhs.errorClientDescription == rhs.errorClientDescription && lhs.duration == rhs.duration && lhs.isFinished == rhs.isFinished && lhs.timeout == rhs.timeout
  }

  public func hash(into hasher: inout Hasher) {
    hasher.combine(id)
    hasher.combine(url)
    hasher.combine(host)
    hasher.combine(port)
    hasher.combine(scheme)
    hasher.combine(date)
    hasher.combine(method)
    hasher.combine(headers)
    hasher.combine(credentials)
    hasher.combine(cookies)
    hasher.combine(httpBody)
    hasher.combine(code)
    hasher.combine(responseHeaders)
    hasher.combine(responseData)
    hasher.combine(errorClientDescription)
    hasher.combine(duration)
    hasher.combine(isFinished)
    hasher.combine(timeout)
  }

  public func hostOrPath() -> String {
    if let url = URL(string: url) {
      if url.path.isEmpty {
        return url.host ?? url.absoluteString
      } else {
        return "/\(url.lastPathComponent)"
      }
    }
    return url
  }
}

extension OGNetworkLoggerRequestModel {
  static let fallbackString = "NA"

  public var exportString: String {
    """
    URL: \(url)
    Host: \(host ?? Self.fallbackString)
    Port: \(port ?? -1)
    Scheme: \(scheme ?? Self.fallbackString)
    Date: \(date)
    Method: \(method)
    Headers: \(headers)
    Credentials: \(credentials)
    Cookies: \(cookies ?? Self.fallbackString)
    Body: \(String(data: httpBody ?? Data(), encoding: .utf8) ?? Self.fallbackString)
    Status code: \(code)
    Response headers: \(responseHeaders ?? [:])
    Response body: \(String(data: responseData ?? Data(), encoding: .utf8) ?? Self.fallbackString)
    \(!(errorClientDescription ?? "").isEmpty ? "Error description: \(errorClientDescription ?? "")" : "")
    Duration: \(duration ?? Double())
    """
  }
}
