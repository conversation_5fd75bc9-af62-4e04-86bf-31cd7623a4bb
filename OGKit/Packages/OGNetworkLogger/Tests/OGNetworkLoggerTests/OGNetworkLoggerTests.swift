import Combine
import OGDIService
@testable import OGNetworkLogger
import XCTest

final class OGNetworkLoggerTests: XCTestCase {
  let expectation = XCTestExpectation()
  @OGInjected(\OGNetworkLoggerContainer.ignoreManager) private var ignoreManager
  @OGInjected(\OGNetworkLoggerContainer.networkLogger) private var networkLogger
  @OGInjected(\OGNetworkLoggerContainer.networkLoggerStorage) private var storage

  private var cancellables: Set<AnyCancellable> = []

  override func tearDownWithError() throws {
    storage.clearRequests()
    cancellables.removeAll()
    try super.tearDownWithError()
  }

  func testIgnoreOnUrlRequest() {
    // given
    let url = "https://reqres.in/api/users?page=2"
    ignoreManager.ignore = .enabled(ignoreHandler: { request in
      request.url.contains("reqres")
    })

    // when
    networkLogger.startLogger()

    _ = storage.filteredRequestsPublisher.sink { filteredRequests in
      XCTAssertTrue(filteredRequests.isEmpty)
      self.expectation.fulfill()
    }

    URLSession.shared.dataTask(with: URL(string: url)!) { [weak self] _, _, _ in
      guard let self else {
        XCTFail()
        return
      }
      // then
      self.storage.requestsPublisher.sink { requests in
        XCTAssertTrue(requests.contains(where: { $0.url == url }))
      }.store(in: &self.cancellables)

    }.resume()

    wait(for: [expectation], timeout: 2)
  }

  func testIgnoreOnHeaderRequest() {
    // given
    let headerKey = "language"
    let headerValue = "rus"
    var request = URLRequest(url: URL(string: "https://reqres.in/api/users?page=2")!)
    request.setValue(headerValue, forHTTPHeaderField: headerKey)
    ignoreManager.ignore = .enabled(ignoreHandler: { request in
      if let value = request.headers[headerKey] {
        return value == headerValue
      }
      return false
    })

    // when
    networkLogger.startLogger()

    _ = storage.filteredRequestsPublisher.sink { filteredRequests in
      XCTAssertTrue(filteredRequests.isEmpty)
      self.expectation.fulfill()
    }

    URLSession.shared.dataTask(with: request) { [weak self] _, _, _ in
      guard let self else {
        XCTFail()
        return
      }
      // then
      self.storage.requestsPublisher.sink { requests in
        XCTAssertTrue(requests.contains(where: { $0.headers.contains { key, value in
          key == headerKey && value == headerValue
        }}))
      }.store(in: &self.cancellables)
    }.resume()

    wait(for: [expectation], timeout: 2)
  }
}
