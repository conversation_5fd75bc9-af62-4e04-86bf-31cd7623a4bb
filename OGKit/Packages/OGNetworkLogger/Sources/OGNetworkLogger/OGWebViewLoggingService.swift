import Foundation
import OGDIService

/// The `OGWebViewLoggingService` is a service that helps logging requests in web views.
public class OGWebViewLoggingService {
  /// Custom errors for handling logging web view requests.
  enum OGWebViewLoggingServiceError: LocalizedError {
    /// Manual logging timeout.
    case loggingTimeout

    public var errorDescription: String? {
      switch self {
      case .loggingTimeout:
        return NSLocalizedString("Reached the timeout limit for the current request", comment: "Timeout error description")
      }
    }
  }

  @OGInjected(\OGNetworkLoggerContainer.networkLoggerStorage) private var storage

  private var currentRequest: OGNetworkLoggerRequestModel?
  private var requestTimeoutTimers: [String: Timer] = [:]
  private let maxTimeout: Double
  private let max32BitInt = Double(Int32.max)

  /// Public initializer
  /// - Parameter maxTimeout: Sets the timeout value for manual logging, determining how long the logger should wait for a request to finish. The default value is 60.0 seconds. Once the timeout limit is reached, the request will be marked as "finished" with a timeout error.
  public init(maxTimeout: Double = 60.0) {
    assert(maxTimeout > .zero)
    self.maxTimeout = maxTimeout
  }

  /// Starts logging the current request. Creates a local `OGNetworkLoggerRequestModel` without a URLSession and uses it for all subsequent functions. The request is saved with the status "in progress".
  /// - Parameter req: The requests to be logged.
  public func startLoggingRequest(_ req: NSURLRequest) {
    DispatchQueue.main.async {
      self.currentRequest = OGNetworkLoggerRequestModel(request: req, session: nil)
      guard let currentRequest = self.currentRequest else { return }
      self.checkTimeoutForRequest(currentRequest)
    }
  }

  /// Marks the current request as "is finished" with the provided response.
  /// - Parameter response: The response corresponding to the current request.
  public func finishLoggingRequest(withResponse response: URLResponse) {
    DispatchQueue.main.async {
      guard let currentRequest = self.currentRequest else { return }
      currentRequest.initResponse(response: response)
      self.saveRequest(currentRequest)
      self.invalidateTimerIfNeeded(forRequest: currentRequest)
    }
  }

  /// Marks the current request as "is finished" with the provided error.
  /// - Parameter error: The error corresponding to the current request.
  public func finishLoggingRequest(withError error: Error) {
    DispatchQueue.main.async {
      guard let currentRequest = self.currentRequest else { return }
      currentRequest.requestFinished(withError: error)
      self.saveRequest(currentRequest)
      self.invalidateTimerIfNeeded(forRequest: currentRequest)
    }
  }

  /// Marks the current request as "finished" without any additional information.
  public func finishRequest() {
    DispatchQueue.main.async {
      guard let currentRequest = self.currentRequest else { return }
      currentRequest.requestFinished()
      self.saveRequest(currentRequest)
      self.invalidateTimerIfNeeded(forRequest: currentRequest)
    }
  }

  private func checkTimeoutForRequest(_ request: OGNetworkLoggerRequestModel) {
    let timeout: Double
    if request.timeout >= max32BitInt {
      timeout = maxTimeout
    } else {
      timeout = maxTimeout > request.timeout ? request.timeout : maxTimeout
    }

    let timer = Timer(timeInterval: timeout, repeats: false) { [weak self] _ in
      if !(self?.storage.request(forId: request.id)?.isFinished ?? true) {
        request.didExceedTimeout = true
        request.requestFinished(withError: OGWebViewLoggingServiceError.loggingTimeout)
        self?.saveRequest(request)
        self?.invalidateTimerIfNeeded(forRequest: request)
      }
    }
    requestTimeoutTimers[request.id] = timer
    timer.fire()
  }

  private func invalidateTimerIfNeeded(forRequest req: OGNetworkLoggerRequestModel) {
    guard let value = requestTimeoutTimers.first(where: { $0.key == req.id })?.value else {
      return
    }
    value.invalidate()
    requestTimeoutTimers.removeValue(forKey: req.id)
  }

  private func saveRequest(_ req: OGNetworkLoggerRequestModel) {
    storage.saveRequest(request: req)
  }
}
