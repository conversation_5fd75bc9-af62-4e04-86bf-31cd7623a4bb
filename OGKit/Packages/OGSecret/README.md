# OGSecret

OGSecret is a package that provides a set of functionalities for publishing secrets into modules.

## [Documentation](https://aacml.github.io/og-dx_aac-ios-docs-otto_group_commerce_kit/ogsecret/documentation/ogsecret/)

## Setup

### OGSecretService
To use OGSecret, you will first need to register your own OGSecretService and provide your secrets as an `OGSecret` set. This must be done in order to publish and notify all implemented adapters.
NOTE: The latest secrets plugin that can be found from the root folder at `Plugins/SecretService` generates a ready-to-use OGSecrets Array.

```swift
import OGSecret

do {
    let service = try OGSecretService(secrets: Set(Secrets.ogSecrets))
    OGSecretServiceContainer.service.register {
        service
    }
} catch {
    print(error.localizedDescription)
}
```

### OGSecretAdapter
The OGSecretAdapter is the adapter from which your custom adapter must inherit in order to access its secret. For this, the secretName must be overwritten.
```swift
import OGSecret

/// Contains the secret if the `secretName` is available in the registered `OGSecretService`
final class MySecretAdapter: OGSecretAdapter {
  override public class var secretName: OGSecret.Name { "MySecret" }
}
```

To access the secret, use the `secret` property of your adapter.
```swift
    let mySecretAdapter = MySecretAdapter()
    let theSecret = mySecretAdapter.secret
```
