import Foundation

// MARK: - NetworkLoggerUrlProtocol

class NetworkLoggerUrlProtocol: URLProtocol {
  enum Constants {
    static let RequestHandledKey = "NetworkLoggerUrlProtocol"
  }

  var session: URLSession?
  var sessionTask: URLSessionDataTask?
  var currentRequest: OGNetworkLoggerRequestModel?
  lazy var requestObserver: RequestObserverProtocol = {
    RequestObserver(options: [
      RequestStorage.shared
    ])
  }()

  override init(request: URLRequest, cachedResponse: CachedURLResponse?, client: URLProtocolClient?) {
    super.init(request: request, cachedResponse: cachedResponse, client: client)

    if session == nil {
      self.session = URLSession(configuration: .default, delegate: self, delegateQueue: nil)
    }
  }

  override class func canInit(with request: URLRequest) -> Bool {
    if NetworkLoggerUrlProtocol.property(forKey: Constants.RequestHandledKey, in: request) != nil {
      return false
    }
    return true
  }

  override class func canonicalRequest(for request: URLRequest) -> URLRequest {
    request
  }

  override func startLoading() {
    let newRequest = ((request as NSURLRequest).mutableCopy() as? NSMutableURLRequest)!
    NetworkLoggerUrlProtocol.setProperty(true, forKey: Constants.RequestHandledKey, in: newRequest)
    sessionTask = session?.dataTask(with: newRequest as URLRequest)
    sessionTask?.resume()

    currentRequest = OGNetworkLoggerRequestModel(request: newRequest, session: session)
    if let request = currentRequest {
      requestObserver.newRequestArrived(request)
    }
  }

  override func stopLoading() {
    sessionTask?.cancel()
    currentRequest?.httpBody = body(from: request)
    if let startDate = currentRequest?.date {
      currentRequest?.duration = fabs(startDate.timeIntervalSinceNow) * 1_000 // Find elapsed time and convert to milliseconds
    }
    currentRequest?.isFinished = true

    if let request = currentRequest {
      requestObserver.newRequestArrived(request)
    }
    session?.invalidateAndCancel()
  }

  private func body(from request: URLRequest) -> Data? {
    // The receiver will have either an HTTP body or an HTTP body stream only one may be set for a request.
    // A HTTP body stream is preserved when copying an NSURLRequest object,
    // but is lost when a request is archived using the NSCoding protocol.
    request.httpBody ?? request.getHttpBodyStreamData()
  }

  deinit {
    session = nil
    sessionTask = nil
    currentRequest = nil
  }
}

// MARK: URLSessionDataDelegate

extension NetworkLoggerUrlProtocol: URLSessionDataDelegate {
  func urlSession(_: URLSession, dataTask _: URLSessionDataTask, didReceive data: Data) {
    client?.urlProtocol(self, didLoad: data)
    if currentRequest?.responseData == nil {
      currentRequest?.responseData = data
    } else {
      currentRequest?.responseData?.append(data)
    }
  }

  func urlSession(_: URLSession, dataTask _: URLSessionDataTask, didReceive response: URLResponse, completionHandler: @escaping (URLSession.ResponseDisposition) -> Void) {
    let policy = URLCache.StoragePolicy(rawValue: request.cachePolicy.rawValue) ?? .notAllowed
    client?.urlProtocol(self, didReceive: response, cacheStoragePolicy: policy)
    currentRequest?.initResponse(response: response)
    completionHandler(.allow)
  }

  func urlSession(_: URLSession, task _: URLSessionTask, didCompleteWithError error: Error?) {
    if let error {
      currentRequest?.errorClientDescription = error.localizedDescription
      client?.urlProtocol(self, didFailWithError: error)
    } else {
      client?.urlProtocolDidFinishLoading(self)
    }
  }

  func urlSession(_: URLSession, task _: URLSessionTask, willPerformHTTPRedirection response: HTTPURLResponse, newRequest request: URLRequest, completionHandler: @escaping (URLRequest?) -> Void) {
    client?.urlProtocol(self, wasRedirectedTo: request, redirectResponse: response)
    completionHandler(request)
  }

  func urlSession(_: URLSession, didBecomeInvalidWithError error: Error?) {
    guard let error else { return }
    currentRequest?.errorClientDescription = error.localizedDescription
    client?.urlProtocol(self, didFailWithError: error)
  }

  func urlSession(_: URLSession, didReceive challenge: URLAuthenticationChallenge, completionHandler: @escaping (URLSession.AuthChallengeDisposition, URLCredential?) -> Void) {
    let protectionSpace = challenge.protectionSpace
    let sender = challenge.sender

    if protectionSpace.authenticationMethod == NSURLAuthenticationMethodServerTrust {
      if let serverTrust = protectionSpace.serverTrust {
        let credential = URLCredential(trust: serverTrust)
        sender?.use(credential, for: challenge)
        completionHandler(.useCredential, credential)
        return
      }
    }
  }

  func urlSessionDidFinishEvents(forBackgroundURLSession _: URLSession) {
    client?.urlProtocolDidFinishLoading(self)
  }
}
