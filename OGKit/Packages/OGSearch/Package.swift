// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "OGSearch",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGSearch",
      targets: ["OGSearch"]
    )
  ],
  dependencies: [
    .package(path: "../OGCore"),
    .package(path: "../OGExternalDependencies/OGDIService"),
    .package(path: "../OGFeatureKit/Packages/OGFeatureAdapter"),
    .package(path: "../OGFeatureKit/Packages/OGFeatureCore"),
    .package(path: "../OGL10n")
  ],
  targets: [
    .target(
      name: "OGSearch",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGFeatureAdapter",
        "OGFeatureCore",
        "OGL10n"
      ]
    ),
    .testTarget(
      name: "OGSearchTests",
      dependencies: ["OGSearch"]
    )
  ]
)
