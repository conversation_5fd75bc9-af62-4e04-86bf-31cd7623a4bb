// swiftlint:disable type_name

import SwiftUI

// MARK: - OGNavigationBarSearchFieldIconStyleKey

struct OGNavigationBarSearchFieldIconStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGNavigationBarSearchFieldIconStyle(style: DefaultOGNavigationBarSearchFieldIconStyle())
}

extension EnvironmentValues {
  var styleOGNavigationBarSearchFieldIcon: AnyOGNavigationBarSearchFieldIconStyle {
    get { self[OGNavigationBarSearchFieldIconStyleKey.self] }
    set { self[OGNavigationBarSearchFieldIconStyleKey.self] = newValue }
  }
}
