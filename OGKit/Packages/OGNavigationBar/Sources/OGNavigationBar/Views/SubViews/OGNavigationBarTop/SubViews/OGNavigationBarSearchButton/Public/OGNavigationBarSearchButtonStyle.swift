import SwiftUI

/// A type that applies custom appearance to
/// all OGNavigationBarSearchButtons within a view hierarchy.
public protocol OGNavigationBarSearchButtonStyle {
  /// A view that represents the body of a OGNavigationBarSearchButton.
  associatedtype Body: View

  /// The properties of a OGNavigationBarSearchButton.
  typealias Configuration = OGNavigationBarSearchButtonStyleConfiguration

  /// Creates a view that represents the body of a OGNavigationBarSearchButton.
  ///
  /// The system calls this method for each OGNavigationBarSearchButton instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGNavigationBarSearchButtonStyle``.
  /// ```swift
  /// struct MyCustomOGNavigationBarSearchButtonStyle: OGNavigationBarSearchButtonStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGNavigationBarSearchButtonConfiguration`` of the  OGNavigationBarSearchButton.
  /// - Returns: A view that represents a OGNavigationBarSearchButton.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
