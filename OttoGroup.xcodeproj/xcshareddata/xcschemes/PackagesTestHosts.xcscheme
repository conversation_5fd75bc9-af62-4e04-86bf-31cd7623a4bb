<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1600"
   version = "1.7">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES"
      runPostActionsOnFailure = "NO">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "NO"
            buildForProfiling = "NO"
            buildForArchiving = "NO"
            buildForAnalyzing = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "8305ECA24D262C3BB9681D98"
               BuildableName = "PackagesTestHosts.app"
               BlueprintName = "PackagesTestHosts"
               ReferencedContainer = "container:OttoGroup.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Alpha (Debug)"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      onlyGenerateCoverageForSpecifiedTargets = "NO">
      <MacroExpansion>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8305ECA24D262C3BB9681D98"
            BuildableName = "PackagesTestHosts.app"
            BlueprintName = "PackagesTestHosts"
            ReferencedContainer = "container:OttoGroup.xcodeproj">
         </BuildableReference>
      </MacroExpansion>
      <Testables>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "RecommendationTests"
               BuildableName = "RecommendationTests"
               BlueprintName = "RecommendationTests"
               ReferencedContainer = "container:Packages/Recommendation//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "TenantChooserTests"
               BuildableName = "TenantChooserTests"
               BlueprintName = "TenantChooserTests"
               ReferencedContainer = "container:Packages/TenantChooser//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "PushPromotionTests"
               BuildableName = "PushPromotionTests"
               BlueprintName = "PushPromotionTests"
               ReferencedContainer = "container:Packages/PushPromotion//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "ProductReviewDetailTests"
               BuildableName = "ProductReviewDetailTests"
               BlueprintName = "ProductReviewDetailTests"
               ReferencedContainer = "container:Packages/ProductReviewDetail//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "UICatalogTests"
               BuildableName = "UICatalogTests"
               BlueprintName = "UICatalogTests"
               ReferencedContainer = "container:Packages/UICatalog//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "InboxTests"
               BuildableName = "InboxTests"
               BlueprintName = "InboxTests"
               ReferencedContainer = "container:Packages/Inbox//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGAsyncImageTests"
               BuildableName = "OGAsyncImageTests"
               BlueprintName = "OGAsyncImageTests"
               ReferencedContainer = "container:Packages/OGAsyncImage//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "ExternalDependenciesTests"
               BuildableName = "ExternalDependenciesTests"
               BlueprintName = "ExternalDependenciesTests"
               ReferencedContainer = "container:Packages/ExternalDependencies//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "ProductDetailTests"
               BuildableName = "ProductDetailTests"
               BlueprintName = "ProductDetailTests"
               ReferencedContainer = "container:Packages/ProductDetail//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "AppCoreTests"
               BuildableName = "AppCoreTests"
               BlueprintName = "AppCoreTests"
               ReferencedContainer = "container:Packages/AppCore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "TabBarTests"
               BuildableName = "TabBarTests"
               BlueprintName = "TabBarTests"
               ReferencedContainer = "container:Packages/TabBar//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "DealsTests"
               BuildableName = "DealsTests"
               BlueprintName = "DealsTests"
               ReferencedContainer = "container:Packages/Deals//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "StoreFinderTests"
               BuildableName = "StoreFinderTests"
               BlueprintName = "StoreFinderTests"
               ReferencedContainer = "container:Packages/StoreFinder//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "WelcomeTests"
               BuildableName = "WelcomeTests"
               BlueprintName = "WelcomeTests"
               ReferencedContainer = "container:Packages/Welcome//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "SearchTests"
               BuildableName = "SearchTests"
               BlueprintName = "SearchTests"
               ReferencedContainer = "container:Packages/Search//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "AppUpdateTests"
               BuildableName = "AppUpdateTests"
               BlueprintName = "AppUpdateTests"
               ReferencedContainer = "container:Packages/AppUpdate//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "PromoBannerTests"
               BuildableName = "PromoBannerTests"
               BlueprintName = "PromoBannerTests"
               ReferencedContainer = "container:Packages/PromoBanner//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "TenantChooserCoreTests"
               BuildableName = "TenantChooserCoreTests"
               BlueprintName = "TenantChooserCoreTests"
               ReferencedContainer = "container:Packages/TenantChooserCore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "ReviewTests"
               BuildableName = "ReviewTests"
               BlueprintName = "ReviewTests"
               ReferencedContainer = "container:Packages/Review//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGAppTests"
               BuildableName = "OGAppTests"
               BlueprintName = "OGAppTests"
               ReferencedContainer = "container:Packages/OGApp//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "NativeAPITests"
               BuildableName = "NativeAPITests"
               BlueprintName = "NativeAPITests"
               ReferencedContainer = "container:Packages/NativeAPI//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "AssortmentTests"
               BuildableName = "AssortmentTests"
               BlueprintName = "AssortmentTests"
               ReferencedContainer = "container:Packages/Assortment//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "TenantSwitchTests"
               BuildableName = "TenantSwitchTests"
               BlueprintName = "TenantSwitchTests"
               ReferencedContainer = "container:Packages/TenantSwitch//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "AccountTests"
               BuildableName = "AccountTests"
               BlueprintName = "AccountTests"
               ReferencedContainer = "container:Packages/Account//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "CatalogScannerTests"
               BuildableName = "CatalogScannerTests"
               BlueprintName = "CatalogScannerTests"
               ReferencedContainer = "container:Packages/CatalogScanner//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "LoginTests"
               BuildableName = "LoginTests"
               BlueprintName = "LoginTests"
               ReferencedContainer = "container:Packages/Login//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "BraFittingGuideTests"
               BuildableName = "BraFittingGuideTests"
               BlueprintName = "BraFittingGuideTests"
               ReferencedContainer = "container:Packages/BraFittingGuide//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "SalutationTests"
               BuildableName = "SalutationTests"
               BlueprintName = "SalutationTests"
               ReferencedContainer = "container:Packages/Salutation//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "AppTrackerTests"
               BuildableName = "AppTrackerTests"
               BlueprintName = "AppTrackerTests"
               ReferencedContainer = "container:Packages/AppTracker//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGDialogCoordinatorTests"
               BuildableName = "OGDialogCoordinatorTests"
               BlueprintName = "OGDialogCoordinatorTests"
               ReferencedContainer = "container:OGKit/Packages/OGDialogCoordinator//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGURLCredentialStorageTests"
               BuildableName = "OGURLCredentialStorageTests"
               BlueprintName = "OGURLCredentialStorageTests"
               ReferencedContainer = "container:OGKit/Packages/OGURLCredentialStorage//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGBadgeTests"
               BuildableName = "OGBadgeTests"
               BlueprintName = "OGBadgeTests"
               ReferencedContainer = "container:OGKit/Packages/OGBadge//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGUserCoreTests"
               BuildableName = "OGUserCoreTests"
               BlueprintName = "OGUserCoreTests"
               ReferencedContainer = "container:OGKit/Packages/OGUserCore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGDomainStoreTests"
               BuildableName = "OGDomainStoreTests"
               BlueprintName = "OGDomainStoreTests"
               ReferencedContainer = "container:OGKit/Packages/OGDomainStore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGNavigationTests"
               BuildableName = "OGNavigationTests"
               BlueprintName = "OGNavigationTests"
               ReferencedContainer = "container:OGKit/Packages/OGNavigation//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGBundledFeatureSetFetcherTests"
               BuildableName = "OGBundledFeatureSetFetcherTests"
               BlueprintName = "OGBundledFeatureSetFetcherTests"
               ReferencedContainer = "container:OGKit/Packages/OGFeatureKit/Packages/OGBundledFeatureSetFetcher//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGRemoteFeatureSetFetcherTests"
               BuildableName = "OGRemoteFeatureSetFetcherTests"
               BlueprintName = "OGRemoteFeatureSetFetcherTests"
               ReferencedContainer = "container:OGKit/Packages/OGFeatureKit/Packages/OGRemoteFeatureSetFetcher//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGFeatureCoreTests"
               BuildableName = "OGFeatureCoreTests"
               BlueprintName = "OGFeatureCoreTests"
               ReferencedContainer = "container:OGKit/Packages/OGFeatureKit/Packages/OGFeatureCore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGFeatureAdapterTests"
               BuildableName = "OGFeatureAdapterTests"
               BlueprintName = "OGFeatureAdapterTests"
               ReferencedContainer = "container:OGKit/Packages/OGFeatureKit/Packages/OGFeatureAdapter//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGTenantSwitchTests"
               BuildableName = "OGTenantSwitchTests"
               BlueprintName = "OGTenantSwitchTests"
               ReferencedContainer = "container:OGKit/Packages/OGFeatureKit/Packages/OGTenantSwitch//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGFeatureConfigViewTests"
               BuildableName = "OGFeatureConfigViewTests"
               BlueprintName = "OGFeatureConfigViewTests"
               ReferencedContainer = "container:OGKit/Packages/OGFeatureKit/Packages/OGFeatureConfigView//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGTenantCoreTests"
               BuildableName = "OGTenantCoreTests"
               BlueprintName = "OGTenantCoreTests"
               ReferencedContainer = "container:OGKit/Packages/OGFeatureKit/Packages/OGTenantCore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGTestEnvironmentKitTests"
               BuildableName = "OGTestEnvironmentKitTests"
               BlueprintName = "OGTestEnvironmentKitTests"
               ReferencedContainer = "container:OGKit/Packages/OGFeatureKit/Packages/OGTestEnvironmentKit//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGTenantKitTests"
               BuildableName = "OGTenantKitTests"
               BlueprintName = "OGTenantKitTests"
               ReferencedContainer = "container:OGKit/Packages/OGFeatureKit/Packages/OGTenantKit//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGHTTPClientTests"
               BuildableName = "OGHTTPClientTests"
               BlueprintName = "OGHTTPClientTests"
               ReferencedContainer = "container:OGKit/Packages/OGHTTPClient//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGWebBridgeTests"
               BuildableName = "OGWebBridgeTests"
               BlueprintName = "OGWebBridgeTests"
               ReferencedContainer = "container:OGKit/Packages/OGWebBridge//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGAppLifecycleTests"
               BuildableName = "OGAppLifecycleTests"
               BlueprintName = "OGAppLifecycleTests"
               ReferencedContainer = "container:OGKit/Packages/OGAppLifecycle//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGDetectiveComponentTests"
               BuildableName = "OGDetectiveComponentTests"
               BlueprintName = "OGDetectiveComponentTests"
               ReferencedContainer = "container:OGKit/Packages/OGDetectiveComponent//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGInAppBrowserTests"
               BuildableName = "OGInAppBrowserTests"
               BlueprintName = "OGInAppBrowserTests"
               ReferencedContainer = "container:OGKit/Packages/OGInAppBrowser//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGDeepLinkHandlerTests"
               BuildableName = "OGDeepLinkHandlerTests"
               BlueprintName = "OGDeepLinkHandlerTests"
               ReferencedContainer = "container:OGKit/Packages/OGDeepLinkHandler//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGExternalDependenciesTests"
               BuildableName = "OGExternalDependenciesTests"
               BlueprintName = "OGExternalDependenciesTests"
               ReferencedContainer = "container:OGKit/Packages/OGExternalDependencies//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGCopyCodeBannerTests"
               BuildableName = "OGCopyCodeBannerTests"
               BlueprintName = "OGCopyCodeBannerTests"
               ReferencedContainer = "container:OGKit/Packages/OGCopyCodeBanner//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGViewStoreTests"
               BuildableName = "OGViewStoreTests"
               BlueprintName = "OGViewStoreTests"
               ReferencedContainer = "container:OGKit/Packages/OGViewStore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGAdjustReporterTests"
               BuildableName = "OGAdjustReporterTests"
               BlueprintName = "OGAdjustReporterTests"
               ReferencedContainer = "container:OGKit/Packages/OGTracker/Packages/OGAdjustReporter//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGAirshipReporterTests"
               BuildableName = "OGAirshipReporterTests"
               BlueprintName = "OGAirshipReporterTests"
               ReferencedContainer = "container:OGKit/Packages/OGTracker/Packages/OGAirshipReporter//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGFirebaseReporterTests"
               BuildableName = "OGFirebaseReporterTests"
               BlueprintName = "OGFirebaseReporterTests"
               ReferencedContainer = "container:OGKit/Packages/OGTracker/Packages/OGFirebaseReporter//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGTrackerCoreTests"
               BuildableName = "OGTrackerCoreTests"
               BlueprintName = "OGTrackerCoreTests"
               ReferencedContainer = "container:OGKit/Packages/OGTracker/Packages/OGTrackerCore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGWebBridgeTrackerTests"
               BuildableName = "OGWebBridgeTrackerTests"
               BlueprintName = "OGWebBridgeTrackerTests"
               ReferencedContainer = "container:OGKit/Packages/OGTracker/Packages/OGWebBridgeTracker//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGTrackerOptInServiceTests"
               BuildableName = "OGTrackerOptInServiceTests"
               BlueprintName = "OGTrackerOptInServiceTests"
               ReferencedContainer = "container:OGKit/Packages/OGTracker/Packages/OGTrackerOptInService//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGTrackerTests"
               BuildableName = "OGTrackerTests"
               BlueprintName = "OGTrackerTests"
               ReferencedContainer = "container:OGKit/Packages/OGTracker//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGRouterTests"
               BuildableName = "OGRouterTests"
               BlueprintName = "OGRouterTests"
               ReferencedContainer = "container:OGKit/Packages/OGRouter//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGL10nTests"
               BuildableName = "OGL10nTests"
               BlueprintName = "OGL10nTests"
               ReferencedContainer = "container:OGKit/Packages/OGL10n//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGZipArchiverTests"
               BuildableName = "OGZipArchiverTests"
               BlueprintName = "OGZipArchiverTests"
               ReferencedContainer = "container:OGKit/Packages/OGCore/Packages/OGZipArchiver//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGStorageTests"
               BuildableName = "OGStorageTests"
               BlueprintName = "OGStorageTests"
               ReferencedContainer = "container:OGKit/Packages/OGCore/Packages/OGStorage//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGIdentifierTests"
               BuildableName = "OGIdentifierTests"
               BlueprintName = "OGIdentifierTests"
               ReferencedContainer = "container:OGKit/Packages/OGCore/Packages/OGIdentifier//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGMacrosTests"
               BuildableName = "OGMacrosTests"
               BlueprintName = "OGMacrosTests"
               ReferencedContainer = "container:OGKit/Packages/OGCore/Packages/OGMacros//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGCoreTests"
               BuildableName = "OGCoreTests"
               BlueprintName = "OGCoreTests"
               ReferencedContainer = "container:OGKit/Packages/OGCore//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGFirebaseKitTests"
               BuildableName = "OGFirebaseKitTests"
               BlueprintName = "OGFirebaseKitTests"
               ReferencedContainer = "container:OGKit/Packages/OGFirebaseKit//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGWebViewTests"
               BuildableName = "OGWebViewTests"
               BlueprintName = "OGWebViewTests"
               ReferencedContainer = "container:OGKit/Packages/OGWebView//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGSecretTests"
               BuildableName = "OGSecretTests"
               BlueprintName = "OGSecretTests"
               ReferencedContainer = "container:OGKit/Packages/OGSecret//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGNavigationBarTests"
               BuildableName = "OGNavigationBarTests"
               BlueprintName = "OGNavigationBarTests"
               ReferencedContainer = "container:OGKit/Packages/OGNavigationBar//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGSalutationTests"
               BuildableName = "OGSalutationTests"
               BlueprintName = "OGSalutationTests"
               ReferencedContainer = "container:OGKit/Packages/OGSalutation//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGAirshipKitTests"
               BuildableName = "OGAirshipKitTests"
               BlueprintName = "OGAirshipKitTests"
               ReferencedContainer = "container:OGKit/Packages/OGAirshipKit//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGLoginButtonTests"
               BuildableName = "OGLoginButtonTests"
               BlueprintName = "OGLoginButtonTests"
               ReferencedContainer = "container:OGKit/Packages/OGLoginButton//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGUserAgentTests"
               BuildableName = "OGUserAgentTests"
               BlueprintName = "OGUserAgentTests"
               ReferencedContainer = "container:OGKit/Packages/OGUserAgent//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGSystemKitTests"
               BuildableName = "OGSystemKitTests"
               BlueprintName = "OGSystemKitTests"
               ReferencedContainer = "container:OGKit/Packages/OGSystemKit//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGSearchTests"
               BuildableName = "OGSearchTests"
               BlueprintName = "OGSearchTests"
               ReferencedContainer = "container:OGKit/Packages/OGSearch//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGNetworkLoggerTests"
               BuildableName = "OGNetworkLoggerTests"
               BlueprintName = "OGNetworkLoggerTests"
               ReferencedContainer = "container:OGKit/Packages/OGNetworkLogger//">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO"
            parallelizable = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "OGExternalBrowserTests"
               BuildableName = "OGExternalBrowserTests"
               BlueprintName = "OGExternalBrowserTests"
               ReferencedContainer = "container:OGKit/Packages/OGExternalBrowser//">
            </BuildableReference>
         </TestableReference>
      </Testables>
      <CommandLineArguments>
      </CommandLineArguments>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Alpha (Debug)"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8305ECA24D262C3BB9681D98"
            BuildableName = "PackagesTestHosts.app"
            BlueprintName = "PackagesTestHosts"
            ReferencedContainer = "container:OttoGroup.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
      </CommandLineArguments>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "8305ECA24D262C3BB9681D98"
            BuildableName = "PackagesTestHosts.app"
            BlueprintName = "PackagesTestHosts"
            ReferencedContainer = "container:OttoGroup.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
      <CommandLineArguments>
      </CommandLineArguments>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Alpha">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>
