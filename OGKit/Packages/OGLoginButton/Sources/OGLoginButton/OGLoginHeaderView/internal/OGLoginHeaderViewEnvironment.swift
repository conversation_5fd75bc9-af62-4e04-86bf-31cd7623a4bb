// swiftlint:disable type_name

import SwiftUI

// MARK: - OGLoginHeaderViewStyleKey

struct OGLoginHeaderViewStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGLoginHeaderViewStyle(style: DefaultOGLoginHeaderViewStyle())
}

extension EnvironmentValues {
  var styleOGLoginHeaderView: AnyOGLoginHeaderViewStyle {
    get { self[OGLoginHeaderViewStyleKey.self] }
    set { self[OGLoginHeaderViewStyleKey.self] = newValue }
  }
}
