import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGNavigationBarSearchButtonStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGNavigationBarSearchButton instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGNavigationBarSearchButtonStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGNavigationBarSearchButtonStyle``.
  /// - Returns: A view that represents a OGNavigationBarSearchButton.
  @ViewBuilder
  public func register(_ style: some OGNavigationBarSearchButtonStyle) -> some View {
    environment(\.styleOGNavigationBarSearchButton, AnyOGNavigationBarSearchButtonStyle(style: style))
  }
}
