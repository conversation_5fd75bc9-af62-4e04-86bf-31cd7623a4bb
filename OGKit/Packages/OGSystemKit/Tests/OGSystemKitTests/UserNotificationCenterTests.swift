import OGSystemKit
import OGSystemKitTestsUtils
import XCTest

final class UserNotificationCenterTests: XCTestCase {
  func testAuthorizationStatusReturnsCorrectValue() async {
    let sut = OGUserNotificationCenter(getAuthorizationStatus: {
      .notDetermined
    }, requestAuthorization: { _ in
      true
    })
    let status = await sut.authorizationStatus()
    let returnedRequest = await sut.requestAuthorization(options: [])

    XCTAssertEqual(status, .notDetermined)
    XCTAssertEqual(returnedRequest, true)
  }
}
