// swift-tools-version: 5.9

import PackageDescription

let package = Package(
  name: "OGLoginButton",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGLoginButton",
      targets: ["OGLoginButton"]
    ),
    .library(
      name: "OGLoginButtonTestsUtils",
      targets: ["OGLoginButtonTestsUtils"]
    )
  ],
  dependencies: [
    .package(
      path: "../OGCore"
    ),
    .package(
      path: "../OGAppLifecycle"
    ),
    .package(
      path: "../OGFeatureKit/Packages/OGFeatureAdapter"
    ),
    .package(
      path: "../OGExternalDependencies/OGDIService"
    ),
    .package(
      path: "../OGUserCore"
    )
  ],
  targets: [
    .target(
      name: "OGLoginButton",
      dependencies: [
        "OGAppLifecycle",
        "OGCore",
        "OGDIService",
        "OGFeatureAdapter",
        "OGUserCore"
      ]
    ),
    .target(
      name: "OGLoginButtonTestsUtils",
      dependencies: [
        "OGLoginButton",
        .product(name: "OGCoreTestsUtils", package: "OGCore")
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGLoginButtonTests",
      dependencies: ["OGLoginButtonTestsUtils"]
    )
  ]
)
