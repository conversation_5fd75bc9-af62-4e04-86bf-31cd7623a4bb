// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGLoginHeaderView.
public struct OGLoginHeaderViewStyleConfiguration {
  /// A view that describes the content of a the OGLoginHeaderView.
  public let content: OGLoginHeaderViewStyleConfiguration.Content

  /// The type-erased content of a OGLoginHeaderView.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public private(set) var body: AnyView
  }
}
