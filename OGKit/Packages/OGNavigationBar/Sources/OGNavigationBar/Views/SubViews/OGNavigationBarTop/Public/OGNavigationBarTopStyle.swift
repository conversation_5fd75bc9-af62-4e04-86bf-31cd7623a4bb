import SwiftUI

/// A type that applies custom appearance to
/// all OGNavigationBarTops within a view hierarchy.
public protocol OGNavigationBarTopStyle {
  /// A view that represents the body of a OGNavigationBarTop.
  associatedtype Body: View

  /// The properties of a OGNavigationBarTop.
  typealias Configuration = OGNavigationBarTopStyleConfiguration

  /// Creates a view that represents the body of a OGNavigationBarTop.
  ///
  /// The system calls this method for each OGNavigationBarTop instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGNavigationBarTopStyle``.
  /// ```swift
  /// struct MyCustomOGNavigationBarTopStyle: OGNavigationBarTopStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGNavigationBarTopConfiguration`` of the  OGNavigationBarTop.
  /// - Returns: A view that represents a OGNavigationBarTop.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
