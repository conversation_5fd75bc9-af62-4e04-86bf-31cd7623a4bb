// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGNavigationBarSearchFieldText.
public struct OGNavigationBarSearchFieldTextStyleConfiguration {
  /// A view that describes the content of a the OGNavigationBarSearchFieldText.
  public let content: OGNavigationBarSearchFieldTextStyleConfiguration.Content

  /// The type-erased content of a OGNavigationBarSearchFieldText.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public var body: AnyView
  }
}
