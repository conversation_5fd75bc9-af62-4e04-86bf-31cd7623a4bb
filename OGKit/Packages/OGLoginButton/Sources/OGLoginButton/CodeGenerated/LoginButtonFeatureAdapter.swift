// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Combine
import Foundation
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros


public protocol LoginButtonFeatureConfigurable {
  var isEnabled: Bool { get set }
  
  var navigationTitle: LoginButtonConfiguration { get set }
}

public struct LoginButtonConfiguration: Equatable, Codable {
  public let enabledUrls: [String]
  
  public init(enabledUrls: [String]) {
    self.enabledUrls = enabledUrls
  }
}

public protocol LoginButtonFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<LoginButtonFeatureConfigurable, Never> { get }
}

public final class LoginButtonFeatureAdapter: OGFeatureAdapter, LoginButtonFeatureAdaptable {
  override public class var featureName: OGFeature.Name { OGIdentifier.loginButton.value }
  
  public let configuration: CurrentValueSubject<LoginButtonFeatureConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()
  
  public init(configuration: LoginButtonFeatureConfigurable?) {
    guard let configuration = configuration else {
      fatalError("The LoginButtonFeatureConfiguration has not been registered")
    }
    self.configuration = CurrentValueSubject(configuration)
    super.init()
    
    receiveUpdates()
  }
  
  private func receiveUpdates() {
    $feature
      .sink { [weak self] feature in
        guard let self = self else { return }
        
        var updatedConfiguration = self.configuration.value
        guard let feature = feature else {
          updatedConfiguration.isEnabled = false
          self.configuration.send(updatedConfiguration)
          return
        }
        updatedConfiguration.isEnabled = feature.isEnabled
        
        let dict: [String: Any] = feature.customValue(for: OGFeatureKey.CustomValues.LoginButton.navigationTitle)
        if let enabledUrls = (dict["enabledUrls"] as? [AnyJSONType]).map({ $0.compactMap { $0.jsonValue as? String } }) {
          updatedConfiguration.navigationTitle = LoginButtonConfiguration(enabledUrls: enabledUrls)
        }

        self.configuration.send(updatedConfiguration)
      }
      .store(in: &subscriptions)
    
  }
}

extension OGFeatureKey.CustomValues {
  enum LoginButton: String, OGKeyReceivable {
    var value: String  {
      rawValue
    }
    
    case navigationTitle = "navigationTitle"
    case url = "url"
  }
}

extension OGIdentifier {
  public static let loginButton = #identifier("loginButton")
}
