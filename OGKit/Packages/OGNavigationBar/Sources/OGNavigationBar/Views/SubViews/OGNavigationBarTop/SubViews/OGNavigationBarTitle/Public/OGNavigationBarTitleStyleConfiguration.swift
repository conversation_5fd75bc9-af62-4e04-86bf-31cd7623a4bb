// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGNavigationBarTitle.
public struct OGNavigationBarTitleStyleConfiguration {
  /// A view that describes the content of a the OGNavigationBarTitle.
  public let content: OGNavigationBarTitleStyleConfiguration.Content

  /// The type-erased content of a OGNavigationBarTitle.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public private(set) var body: AnyView
  }
}
