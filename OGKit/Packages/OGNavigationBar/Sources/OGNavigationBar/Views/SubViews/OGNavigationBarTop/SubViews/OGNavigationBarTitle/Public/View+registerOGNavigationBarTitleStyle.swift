import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGNavigationBarTitleStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGNavigationBarTitle instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGNavigationBarTitleStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGNavigationBarTitleStyle``.
  /// - Returns: A view that represents a OGNavigationBarTitle.
  @ViewBuilder
  public func register(_ style: some OGNavigationBarTitleStyle) -> some View {
    environment(\.styleOGNavigationBarTitle, AnyOGNavigationBarTitleStyle(style: style))
  }
}
