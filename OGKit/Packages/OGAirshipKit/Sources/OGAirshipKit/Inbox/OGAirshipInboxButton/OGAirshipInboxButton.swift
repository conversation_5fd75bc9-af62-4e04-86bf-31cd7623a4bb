// swiftlint:disable type_name

import Foundation
import OGCore
import OGDIService
import OGRouter
import OGViewStore
import SwiftUI

// MARK: - OGAirshipInboxButton

public struct OGAirshipInboxButton: View {
  @Environment(\.styleOGAirshipInboxButton) private var style
  @StateObject private var viewStore: Store
  public init() {
    _viewStore = StateObject(wrappedValue: Self.make())
  }

  public var body: some View {
    if viewStore.isEnabled {
      style.makeBody(configuration: OGAirshipInboxButtonStyleConfiguration(
        content: .init(content: content, unreadCount: viewStore.unreadCount),
        unreadCount: viewStore.unreadCount
      ))
    }
  }

  private var content: some View {
    Button(action: {
      Task {
        await viewStore.dispatch(.didTap)
      }
    }) {
      ZStack {
        OGAirshipInboxIcon()
        if viewStore.unreadCount > 0 {
          OGAirshipInboxIconBadge(unreadCount: viewStore.unreadCount)
        }
      }
    }
    .accessibilityElement(children: .ignore)
  }
}

// MARK: - OGAirshipInboxButton_Previews

struct OGAirshipInboxButton_Previews: PreviewProvider {
  static var previews: some View {
    OGAirshipInboxButton()
  }
}
