// Generated using OGFeatureAdapterMaker
import OGDIService

public final class LoginButtonFeatureAdapterContainer: OGDISharedContainer {

  public private(set) static var shared: LoginButtonFeatureAdapterContainer = .init()
  public private(set) var manager: OGDIContainerManager = .init()
  
  public var loginButton: OGDIService<LoginButtonFeatureAdaptable> {
    self {
      LoginButtonFeatureAdapter(configuration: nil)
    }.cached
  }
  
}
