import AirshipCore
import Combine
import Foundation
import OGAirshipKit
import OGDIService
import OGMacros
import <PERSON>GMock

@OGMock
public class OGAirshipPrivacyManagerMock: AirshipPrivacyManaging {
  public var enabledFeatures: AirshipFeature

  public init(
    enabledFeatures: AirshipFeature = .ogkit
  ) {
    self.enabledFeatures = enabledFeatures
  }

  public func enableFeatures(_ features: AirshipFeature) {
    mock.enableFeatures(features)
  }

  public func disableFeatures(_ features: AirshipFeature) {
    mock.disableFeatures(features)
  }
}
