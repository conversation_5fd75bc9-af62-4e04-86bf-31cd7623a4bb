import SwiftUI

/// A type that applies custom appearance to
/// all OGNavigationBars within a view hierarchy.
public protocol OGNavigationBarStyle {
  /// A view that represents the body of a OGNavigationBar.
  associatedtype Body: View

  /// The properties of a OGNavigationBar.
  typealias Configuration = NavigationBarStyleConfiguration

  /// Creates a view that represents the body of a OGNavigationBar.
  ///
  /// The system calls this method for each OGNavigationBar instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGNavigationBarStyle``.
  /// ```swift
  /// struct MyCustomOGNavigationBarStyle: OGNavigationBarStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGNavigationBarConfiguration`` of the  OGNavigationBar.
  /// - Returns: A view that represents a OGNavigationBar.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
