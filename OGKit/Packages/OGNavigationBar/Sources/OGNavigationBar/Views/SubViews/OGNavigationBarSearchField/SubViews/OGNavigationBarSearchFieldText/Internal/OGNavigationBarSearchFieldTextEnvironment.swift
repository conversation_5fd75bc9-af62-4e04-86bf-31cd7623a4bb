// swiftlint:disable type_name

import SwiftUI

// MARK: - OGNavigationBarSearchFieldTextStyleKey

struct OGNavigationBarSearchFieldTextStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGNavigationBarSearchFieldTextStyle(style: DefaultOGNavigationBarSearchFieldTextStyle())
}

extension EnvironmentValues {
  var styleOGNavigationBarSearchFieldText: AnyOGNavigationBarSearchFieldTextStyle {
    get { self[OGNavigationBarSearchFieldTextStyleKey.self] }
    set { self[OGNavigationBarSearchFieldTextStyleKey.self] = newValue }
  }
}
