// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
  name: "OGSystemKit",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGSystemKit",
      targets: ["OGSystemKit"]
    ),
    .library(
      name: "OGSystemKitTestsUtils",
      targets: ["OGSystemKitTestsUtils"]
    )
  ],
  dependencies: [
    .package(path: "../OGCore"),
    .package(path: "../OGExternalDependencies/OGDIService"),
    .package(path: "../OGNavigation")
  ],
  targets: [
    .target(
      name: "OGSystemKit",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGNavigation"
      ]
    ),
    .target(
      name: "OGSystemKitTestsUtils",
      dependencies: [
        "OGSystemKit"
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGSystemKitTests",
      dependencies: ["OGSystemKitTestsUtils"]
    )
  ]
)
