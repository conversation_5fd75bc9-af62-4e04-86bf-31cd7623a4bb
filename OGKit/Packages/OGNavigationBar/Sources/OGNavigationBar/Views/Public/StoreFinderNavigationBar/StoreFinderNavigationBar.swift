import OGCore
import SwiftUI

public struct StoreFinderNavigationBar<SegmentedControlView: View, TrailingView: View, SearchBarView: View, ContentView: View>: View {
  @Environment(\.styleOGNavigationBar) private var style
  @State private var state: OGNavigationBarScrollViewState

  private let segmentedControlView: SegmentedControlView
  private let trailingView: TrailingView
  private let contentView: () -> ContentView
  private var searchBarView: SearchBarView

  public init(
    segmentedControlView: SegmentedControlView,
    trailingView: TrailingView,
    searchBarView: SearchBarView,
    @ViewBuilder content: @escaping () -> ContentView
  ) {
    _state = State(
      wrappedValue: OGNavigationBarScrollViewState(
        contentOffset: .zero,
        hasSearchBar: true,
        hasSearchIcon: false,
        hasBackButton: true
      )
    )
    self.segmentedControlView = segmentedControlView
    self.trailingView = trailingView
    self.searchBarView = searchBarView
    self.contentView = content
  }

  public var body: some View {
    VStack(spacing: .zero, content: {
      style.makeBody(
        configuration: NavigationBarStyleConfiguration(
          content: NavigationBarStyleConfiguration.Content(
            content: content
          )
        )
      )
      contentView()
      Spacer(minLength: 0)
    })
    .navigationBarHidden(true)
  }

  var content: some View {
    VStack(spacing: .zero) {
      topBar
      searchBarView
    }
    .frame(maxWidth: .infinity)
    .dynamicTypeSize(...DynamicTypeSize.xLarge)
  }

  private var topBar: some View {
    OGNavigationBarTop(
      state: $state,
      leadingView: EmptyView(),
      titleView: segmentedControlView,
      trailingView: trailingView
    )
  }
}
