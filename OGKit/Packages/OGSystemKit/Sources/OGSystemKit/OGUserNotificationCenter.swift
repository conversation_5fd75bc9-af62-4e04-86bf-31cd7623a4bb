import Foundation
import OGCore
import UserNotifications

// MARK: - OGUserNotificationCenterHandling

public protocol OGUserNotificationCenterHandling {
  func authorizationStatus() async -> UNAuthorizationStatus
  func requestAuthorization(options: UNAuthorizationOptions) async -> Bool
}

// MARK: - OGUserNotificationCenter

public struct OGUserNotificationCenter: OGUserNotificationCenterHandling {
  private let getAuthorizationStatus: () async -> UNAuthorizationStatus
  private let _requestAuthorization: (UNAuthorizationOptions) async -> Bool

  public init(
    getAuthorizationStatus: @escaping () async -> UNAuthorizationStatus = UNUserNotificationCenter.current().authorizationStatus,
    requestAuthorization: @escaping (UNAuthorizationOptions) async -> Bool = UNUserNotificationCenter.current().requestAuthorization(options:)
  ) {
    self.getAuthorizationStatus = getAuthorizationStatus
    self._requestAuthorization = requestAuthorization
  }

  public func authorizationStatus() async -> UNAuthorizationStatus {
    await getAuthorizationStatus()
  }

  public func requestAuthorization(options: UNAuthorizationOptions) async -> Bool {
    await _requestAuthorization(options)
  }
}

// MARK: - UNUserNotificationCenter + OGUserNotificationCenter

extension UNUserNotificationCenter {
  public func requestAuthorization(options: UNAuthorizationOptions) async -> Bool {
    let logger: OGLoggable = OGCoreContainer.shared.logger()

    return await withUnsafeContinuation { continuation in
      requestAuthorization(options: options) { success, error in
        if let error {
          logger.log(.critical, domain: .service, message: "Error requesting push authorization: \(error)")
        }
        logger.log(.debug, domain: .service, message: "Result of requesting push authorization: \(success)")
        continuation.resume(returning: success)
      }
    }
  }

  public func authorizationStatus() async -> UNAuthorizationStatus {
    await notificationSettings().authorizationStatus
  }
}
