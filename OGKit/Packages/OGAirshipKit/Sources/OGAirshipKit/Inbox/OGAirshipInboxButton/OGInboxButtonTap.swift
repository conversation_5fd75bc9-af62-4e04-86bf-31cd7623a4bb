import Combine
import Foundation

// MARK: - OGInboxButtonTapPublishing

public protocol OGInboxButtonTapPublishing {
  var didTapPublisher: PassthroughSubject<Void, Never> { get }
  func didTap()
}

// MARK: - OGInboxButtonTapPublisher

public final class OGInboxButtonTapPublisher: OGInboxButtonTapPublishing {
  public private(set) var didTapPublisher: PassthroughSubject<Void, Never> = .init()

  public func didTap() {
    didTapPublisher.send(())
  }
}
