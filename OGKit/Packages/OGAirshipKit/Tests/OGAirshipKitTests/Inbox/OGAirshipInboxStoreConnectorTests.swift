import Combine
import OGAirshipKitTestsUtils
import OGCoreTestsUtils
import XCTest

@testable import OGAirshipKit

final class OGAirshipInboxStoreConnectorTests: XCTestCase {
  func test_WHEN_configured_THEN_listenToMessagesAndUnreadCount_AND_whenUpdatesReceived_THEN_setMessagesDispatched() async throws {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Messages set")

      let inboxService = OGAirshipInboxServiceMock()
      inboxService.mock.watchMessagesCalls.mockCall {
        Just(.stub).eraseToAnyPublisher()
      }
      inboxService.mock.watchUnreadCountCalls.mockCall {
        Just(1).eraseToAnyPublisher()
      }
      let sut = OGAirshipInboxState.Connector(inboxService: inboxService)

      var actualActions = [OGAirshipInboxAction]()
      let dispatch: (OGAirshipInboxAction) -> Void = { action in
        actualActions.append(action)
        expectation.fulfill()
      }

      await sut.configure(dispatch: dispatch)

      await fulfillment(of: [expectation], timeout: 0.1)

      XCTAssertEqual(
        actualActions,
        [._set(messages: .stub, unreadCount: 1)]
      )
    }
  }
}
