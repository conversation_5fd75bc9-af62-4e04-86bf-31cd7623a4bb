import Combine
import OGFeatureAdapter
import OGFeatureCore

// MARK: - OGUserAgentFeatureAdapter

public final class OGUserAgentFeatureAdapter: OGFeatureAdapter, OGUserAgentFeatureAdaptable {
  override public class var featureName: OGFeature.Name { "userAgent" }

  public private(set) var isEnabled: CurrentValueSubject<Bool, Never> = CurrentValueSubject(false)

  public private(set) var isAdjustEnabled: CurrentValueSubject<Bool, Never> = CurrentValueSubject(false)
  public private(set) var isAirshipEnabled: CurrentValueSubject<Bool, Never> = CurrentValueSubject(false)
  public private(set) var isAdvertisingIdEnabled: CurrentValueSubject<Bool, Never> = CurrentValueSubject(false)
  private var subscriptions = Set<AnyCancellable>()

  override public init() {
    super.init()
    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature.sink { [weak self] feature in
      guard let feature else {
        self?.isEnabled.send(false)
        return
      }
      self?.isAdjustEnabled.send(feature.customValue(for: OGFeatureKey.CustomValues.UserAgent.isAdjustEnabled))
      self?.isAirshipEnabled.send(feature.customValue(for: OGFeatureKey.CustomValues.UserAgent.isAirshipEnabled))
      self?.isAdvertisingIdEnabled.send(feature.customValue(for: OGFeatureKey.CustomValues.UserAgent.isAdvertisingIdEnabled))
      self?.isEnabled.send(feature.isEnabled)
    }.store(in: &subscriptions)
  }
}

// MARK: - OGUserAgentFeatureAdaptable

public protocol OGUserAgentFeatureAdaptable: OGFeatureAdaptable {
  var isEnabled: CurrentValueSubject<Bool, Never> { get }
  var isAdjustEnabled: CurrentValueSubject<Bool, Never> { get }
  var isAirshipEnabled: CurrentValueSubject<Bool, Never> { get }
  var isAdvertisingIdEnabled: CurrentValueSubject<Bool, Never> { get }
  var feature: OGFeature? { get }
}

// MARK: - OGFeatureKey.CustomValues.UserAgent

extension OGFeatureKey.CustomValues {
  public enum UserAgent: String, OGKeyReceivable {
    public var value: String {
      rawValue
    }

    case isAdjustEnabled
    case isAirshipEnabled
    case isAdvertisingIdEnabled
  }
}
