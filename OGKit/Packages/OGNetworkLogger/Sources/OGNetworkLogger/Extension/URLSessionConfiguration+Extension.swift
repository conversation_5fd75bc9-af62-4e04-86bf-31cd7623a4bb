import Foundation

extension URLSessionConfiguration {
  @objc
  func fakeProcotolClasses() -> [AnyClass]? {
    guard let fakeProcotolClasses = fakeProcotolClasses() else {
      return []
    }
    var originalProtocolClasses = fakeProcotolClasses.filter {
      $0 != NetworkLoggerUrlProtocol.self
    }
    if OGNetworkLoggerContainer.shared.networkLoggerConfig().loggerEnable {
      originalProtocolClasses.insert(NetworkLoggerUrlProtocol.self, at: 0)
    }
    return originalProtocolClasses
  }
}
