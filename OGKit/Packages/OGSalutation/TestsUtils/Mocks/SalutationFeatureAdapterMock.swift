import Combine
import OGCore
import OGDIService
import OGFeatureKit
import OGIdentifier
import OGSalutation

// MARK: - SalutationFeatureAdapterMock

public final class SalutationFeatureAdapterMock: OGFeatureAdapter, SalutationFeatureAdaptable {
  override public class var featureName: OGFeature.Name {
    OGIdentifier.salutation.value
  }

  public var configuration: CurrentValueSubject<
    SalutationFeatureConfigurable,
    Never
  > = CurrentValueSubject(
    SalutationFeatureConfiguration()
  )

  override public init() {}
}

// MARK: - SalutationFeatureAdapterContainer + AutoRegistering

extension SalutationFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    guard OGCoreContainer.shared.appEnvironment().isTestsBuild else { return }
    salutation.register {
      SalutationFeatureAdapterMock()
    }
  }
}
