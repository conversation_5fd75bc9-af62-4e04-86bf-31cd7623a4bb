import Combine
import OGDIService
import SwiftUI

// MARK: - OGNetworkLoggerable

public protocol OGNetworkLoggerable {
  var requestPublisher: Published<[OGNetworkLoggerRequestModel]>.Publisher { get }

  func startLogger()
  func stopLogger()
}

// MARK: - OGNetworkLogger

public final class OGNetworkLogger: OGNetworkLoggerable, ObservableObject {
  @OGInjected(\OGNetworkLoggerContainer.networkLoggerStorage) private var storage
  @OGInjected(\OGNetworkLoggerContainer.networkLoggerConfig) private var networkLoggerConfig
  public var requestPublisher: Published<[OGNetworkLoggerRequestModel]>.Publisher { $requests }
  @Published public var requests: [OGNetworkLoggerRequestModel] = .init()

  let networkRequestLogger = NetworkRequestLogger()

  private var cancellables: Set<AnyCancellable> = []

  init() {
    listenToRequests()
  }

  private func listenToRequests() {
    storage.filteredRequestsPublisher
      .receive(on: RunLoop.main)
      .sink { [weak self] requests in
        self?.requests = requests
      }.store(in: &cancellables)
  }

  private func checkSwizzling() {
    if networkLoggerConfig.swizzled == false {
      networkRequestLogger.swizzleProtocolClasses()
      networkLoggerConfig.swizzled = true
    }
  }

  public func startLogger() {
    networkRequestLogger.startLogger()
    checkSwizzling()
  }

  public func stopLogger() {
    networkRequestLogger.stopLogger()
    checkSwizzling()
  }
}
