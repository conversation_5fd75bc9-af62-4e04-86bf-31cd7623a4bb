import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGNavigationBarTopStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGNavigationBarTop instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGNavigationBarTopStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGNavigationBarTopStyle``.
  /// - Returns: A view that represents a OGNavigationBarTop.
  @ViewBuilder
  public func register(_ style: some OGNavigationBarTopStyle) -> some View {
    environment(\.styleOGNavigationBarTop, AnyOGNavigationBarTopStyle(style: style))
  }
}
