import ARKit

public final class ARSessionMock: ARSession {
  public private(set) var pauseCallsCount = 0
  override public func pause() {
    pauseCallsCount += 1
  }

  public private(set) var runCallsCount = 0
  public private(set) var runArgs: (ARConfiguration, ARSession.RunOptions)?
  override public func run(_ configuration: ARConfiguration, options: ARSession.RunOptions = []) {
    runArgs = (configuration, options)
  }
}
