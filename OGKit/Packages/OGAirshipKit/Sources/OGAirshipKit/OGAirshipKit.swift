@_exported import AirshipCore

import Combine
import OGCore
import OGDIService
import OGDomainStore

public final class OGAirshipKit {
  private let airshipConfig: OGAirshipConfiguring
  private let airshipSecret: OGAirshipSecretProviding
  private let featureAdapter: AirshipFeatureAdaptable
  private let isFlying: () -> Bool
  private let launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  private let takeOff: (AirshipConfig?, [UIApplication.LaunchOptionsKey: Any]?) -> Void
  private var cancellables = Set<AnyCancellable>()
  private let setEnabled: (Bool) -> Void
  private let airshipPushService: () -> OGAirshipPushServing
  private let inboxServicesSetup: () -> (inboxService: OGAirshipInboxServing, messageCenter: OGAirshipMessageCenterCoordinable)

  @MainActor
  public init(
    airshipConfig: OGAirshipConfiguring = AirshipConfig.config(),
    airshipSecret: OGAirshipSecretProviding = OGAirshipSecret(),
    airshipPushService: @escaping () -> OGAirshipPushServing = { OGAirshipPushService() },
    appEnvironment _: OGAppEnvironmental = OGCoreContainer.shared.appEnvironment(),
    contactAssociationStore _: OGAirshipContactAssociationStore? = OGAirshipContainer.shared.contactAssociationStore(),
    featureAdapter: AirshipFeatureAdaptable = OGAirshipFeatureAdapterContainer.shared.airship(),
    isFlying: @escaping () -> Bool = { Airship.isFlying },
    setEnabled: @escaping (Bool) -> Void = { isEnabled in Airship.push.userPushNotificationsEnabled = isEnabled },
    launchOptions: [UIApplication.LaunchOptionsKey: Any]?,
    takeOff: @escaping (AirshipConfig?, [UIApplication.LaunchOptionsKey: Any]?) -> Void,
    inboxServicesSetup: @escaping () -> (OGAirshipInboxServing, OGAirshipMessageCenterCoordinable) = {
      let inboxService = OGAirshipInboxService()
      let messageCenter = OGAirshipMessageCenterCoordinator(inboxService: inboxService)
      return (inboxService, messageCenter)
    }
  ) {
    self.airshipSecret = airshipSecret
    self.airshipConfig = airshipConfig
    self.airshipPushService = airshipPushService
    self.featureAdapter = featureAdapter
    self.isFlying = isFlying
    self.launchOptions = launchOptions
    self.takeOff = takeOff
    self.setEnabled = setEnabled
    self.inboxServicesSetup = inboxServicesSetup

    setup()
    watchConfig()
  }

  @MainActor
  private func setup() {
    guard
      let key = airshipSecret.key,
      let secret = airshipSecret.secret
    else { return }

    configure(
      appKey: key,
      appSecret: secret,
      site: featureAdapter.configuration.value.cloudSite
    )
  }

  private func watchConfig() {
    featureAdapter
      .configuration
      .sink { [weak self] configuration in
        guard let self, self.isFlying() else { return }
        self.setEnabled(configuration.isEnabled)
      }
      .store(in: &cancellables)
  }

  @MainActor
  private func configure(appKey: String, appSecret: String, site: String) {
    guard !isFlying() else { return }

    var config = airshipConfig
    config.productionAppKey = appKey
    config.productionAppSecret = appSecret
    config.cloudSite = site

    guard let config = config as? AirshipConfig else { return }
    config.inProduction = true
    config.urlAllowListScopeOpenURL = ["*"]

    takeOff(config, launchOptions)

    let inboxServicesSetup = inboxServicesSetup()
    OGAirshipContainer.shared.inboxCoordinator.register {
      inboxServicesSetup.messageCenter
    }

    OGAirshipContainer.shared.inboxService.register {
      inboxServicesSetup.inboxService
    }

    OGAirshipContainer.shared.inboxStore.register {
      OGAirshipInboxStore.make()
    }

    let airshipPushService = airshipPushService()
    OGAirshipContainer.shared.pushService.register {
      airshipPushService
    }

    OGAirshipContainer.shared.channelService.register {
      OGAirshipChannelService()
    }

    OGAirshipContainer.shared.contactService.register {
      OGAirshipContactService()
    }

    OGAirshipContainer.shared.analytics.register {
      OGAirshipAnalytics()
    }

    OGAirshipContainer.shared.contactAssociationStore.register {
      OGAirshipContactAssociationStore.make()
    }

    OGAirshipContainer.shared.channelStore.register {
      OGAirshipChannelStore.make()
    }

    OGAirshipContainer.shared.deviceIdentifier.register {
      Airship.analytics
    }

    OGAirshipContainer.shared.privacyManager.register {
      OGAirshipPrivacyManager()
    }

    OGAirshipContainer.shared.channelID.register {
      OGAirshipChannelIdDialogFeatureAdapter()
    }
  }
}
