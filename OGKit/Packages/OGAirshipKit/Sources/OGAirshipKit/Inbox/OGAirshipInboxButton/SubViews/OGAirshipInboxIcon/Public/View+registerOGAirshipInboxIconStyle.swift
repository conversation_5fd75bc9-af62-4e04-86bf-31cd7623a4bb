import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGAirshipInboxIconStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGAirshipInboxIcon instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGAirshipInboxIconStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGAirshipInboxIconStyle``.
  /// - Returns: A view that represents a OGAirshipInboxIcon.
  @ViewBuilder
  public func register(_ style: some OGAirshipInboxIconStyle) -> some View {
    environment(\.styleOGAirshipInboxIcon, AnyOGAirshipInboxIconStyle(style: style))
  }
}
