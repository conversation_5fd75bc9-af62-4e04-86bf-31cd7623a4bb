import OGCore
import OGDIService
import OGRouter
import OGViewStore

// MARK: - OGNavigationBarSearchField.Store

extension OGNavigationBarSearchField {
  typealias Store = OGViewStore<ViewState, Event>
}

extension OGNavigationBarSearchField {
  @MainActor
  static func make() -> Store {
    OGNavigationBarSearchField.Store(
      reducer: Reducer.reduce,
      middleware: Middleware()
    )
  }
}

// MARK: - OGNavigationBarSearchField.Event

extension OGNavigationBarSearchField {
  enum Event: OGViewEvent {
    case didTap
  }
}

// MARK: - OGNavigationBarSearchField.ViewState

extension OGNavigationBarSearchField {
  struct ViewState: OGViewState {
    static var initial: OGNavigationBarSearchField.ViewState = .init()
  }
}

extension OGNavigationBarSearchField {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _: inout ViewState,
      with _: Event
    ) {}
  }

  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing

    init(router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher()) {
      self.router = router
    }

    func callAsFunction(
      event: Event,
      for _: ViewState
    ) async
      -> Event? {
      switch event {
      case .didTap:
        router.send(OGRoute(OGIdentifier.search.value, data: OGSearchTapOrigin.field))
        return nil
      }
    }
  }
}
