// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGNavigationBarSearchField.
public struct OGNavigationBarSearchFieldStyleConfiguration {
  /// A view that describes the content of a the OGNavigationBarSearchField.
  public let content: OGNavigationBarSearchFieldStyleConfiguration.Content

  /// The type-erased content of a OGNavigationBarSearchField.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public private(set) var body: AnyView
  }
}
