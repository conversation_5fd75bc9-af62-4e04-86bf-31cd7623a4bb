import Foundation

extension URLRequest {
  func getHttpBodyStreamData() -> Data? {
    guard let httpBodyStream else {
      return nil
    }
    let data = NSMutableData()
    var buffer = [UInt8](repeating: 0, count: 4_096)

    httpBodyStream.open()
    while httpBodyStream.hasBytesAvailable {
      let length = httpBodyStream.read(&buffer, maxLength: 4_096)
      if length == 0 {
        break
      } else {
        data.append(&buffer, length: length)
      }
    }
    httpBodyStream.close()
    return data as Data
  }
}
