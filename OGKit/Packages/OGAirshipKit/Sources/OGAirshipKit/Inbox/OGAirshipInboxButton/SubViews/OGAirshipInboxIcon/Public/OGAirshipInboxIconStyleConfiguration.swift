// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGAirshipInboxIcon.
public struct OGAirshipInboxIconStyleConfiguration {
  /// A view that describes the content of a the OGAirshipInboxIcon.
  public let content: OGAirshipInboxIconStyleConfiguration.Content

  /// The type-erased content of a OGAirshipInboxIcon.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public private(set) var body: AnyView
  }
}
