import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGNavigationBarStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGNavigationBar instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGNavigationBarStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGNavigationBarStyle``.
  /// - Returns: A view that represents a OGNavigationBar.
  @ViewBuilder
  public func register(_ style: some OGNavigationBarStyle) -> some View {
    environment(\.styleOGNavigationBar, AnyOGNavigationBarStyle(style: style))
  }
}
