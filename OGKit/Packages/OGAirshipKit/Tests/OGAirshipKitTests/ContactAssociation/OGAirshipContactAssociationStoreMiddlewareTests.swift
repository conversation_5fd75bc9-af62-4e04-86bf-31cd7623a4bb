import OGAirshipKitTestsUtils
import XCTest

@testable import OGAirshipKit

final class OGAirshipContactAssociationStoreMiddlewareTests: XCTestCase {
  func test_WHEN_logOut_AND_shouldNotKeepAssociation_THEN_resetContact() async throws {
    let contactService = OGAirshipContactServiceMock()
    let sut = OGAirshipContactAssociationState.Middleware(
      airshipTrackingFeature: AirshipTrackingFeatureAdapterMock(keepContactAssociation: false),
      contactService: contactService
    )

    let nextAction = try await sut.callAsFunction(
      action: .logOut,
      for: .initial
    )

    XCTAssertEqual(1, contactService.mock.resetContactCalls.callsCount)
    XCTAssertNil(nextAction)
  }

  func test_WHEN_optOut_AND_shouldNotKeepAssociation_THEN_resetContact() async throws {
    let contactService = OGAirshipContactServiceMock()
    let sut = OGAirshipContactAssociationState.Middleware(contactService: contactService)

    let nextAction = try await sut.callAsFunction(
      action: .optOut,
      for: .initial
    )

    XCTAssertEqual(1, contactService.mock.resetContactCalls.callsCount)
    XCTAssertNil(nextAction)
  }

  func test_WHEN_logOut_THEN_noAction() async throws {
    let sut = OGAirshipContactAssociationState.Middleware()

    let nextAction = try await sut.callAsFunction(
      action: .logOut,
      for: .initial
    )

    XCTAssertNil(nextAction)
  }

  func test_WHEN_optIn_THEN_noAction() async throws {
    let sut = OGAirshipContactAssociationState.Middleware()

    let nextAction = try await sut.callAsFunction(
      action: .optIn,
      for: .initial
    )

    XCTAssertNil(nextAction)
  }
}
