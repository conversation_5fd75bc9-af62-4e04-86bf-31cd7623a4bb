// swiftlint:disable type_name

import SwiftUI

struct AnyOGNavigationBarSearchButtonStyle: OGNavigationBarSearchButtonStyle {
  private var _makeBody: (Configuration) -> AnyView

  init(style: some OGNavigationBarSearchButtonStyle) {
    self._makeBody = { configuration in
      AnyView(style.makeBody(configuration: configuration))
    }
  }

  func makeBody(configuration: Configuration) -> some View {
    _makeBody(configuration)
  }
}
