import SwiftUI

/// A type that applies custom appearance to
/// all OGNavigationBarTitles within a view hierarchy.
public protocol OGNavigationBarTitleStyle {
  /// A view that represents the body of a OGNavigationBarTitle.
  associatedtype Body: View

  /// The properties of a OGNavigationBarTitle.
  typealias Configuration = OGNavigationBarTitleStyleConfiguration

  /// Creates a view that represents the body of a OGNavigationBarTitle.
  ///
  /// The system calls this method for each OGNavigationBarTitle instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGNavigationBarTitleStyle``.
  /// ```swift
  /// struct MyCustomOGNavigationBarTitleStyle: OGNavigationBarTitleStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGNavigationBarTitleConfiguration`` of the  OGNavigationBarTitle.
  /// - Returns: A view that represents a OGNavigationBarTitle.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
