// swift-tools-version: 5.9
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
  name: "OGSalutation",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    // Products define the executables and libraries a package produces, making them visible to other packages.
    .library(
      name: "OGSalutation",
      targets: ["OGSalutation"]
    ),
    .library(
      name: "OGSalutationTestsUtils",
      targets: ["OGSalutationTestsUtils"]
    )
  ],
  dependencies: [
    .package(
      path: "../OGCore"
    ),
    .package(
      path: "../OGFeatureKit"
    ),
    .package(
      path: "../OGExternalDependencies/OGDIService"
    )
  ],
  targets: [
    // Targets are the basic building blocks of a package, defining a module or a test suite.
    // Targets can depend on other targets in this package and products from dependencies.
    .target(
      name: "OGSalutation",
      dependencies: [
        "OGCore",
        "OGDIService",
        "OGFeatureKit"
      ]
    ),
    .target(
      name: "OGSalutationTestsUtils",
      dependencies: [
        "OGSalutation"
      ],
      path: "TestsUtils"
    ),
    .testTarget(
      name: "OGSalutationTests",
      dependencies: ["OGSalutation"]
    )
  ]
)
