import AVFoundation
import Foundation
import OGCore
import OGMacros
import <PERSON><PERSON>ock

@OGMock
public class AVCaptureDevicePermissionServiceMock: AVCaptureDevicePermissionServicing {
  public init() {}

  public func continueIfAllowed(for mediaType: AVMediaType) async -> Bool {
    await mock.continueIfAllowed(for: mediaType)
  }

  public func requestPermissionIfNeeded(for mediaType: AVMediaType) async -> Bool {
    await mock.requestPermissionIfNeeded(for: mediaType)
  }

  public func authorizationStatus(for mediaType: AVMediaType) -> AVAuthorizationStatus {
    mock.authorizationStatus(for: mediaType)
  }
}
