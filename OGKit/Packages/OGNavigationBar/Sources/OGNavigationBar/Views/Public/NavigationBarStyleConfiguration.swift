// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGNavigationBar.
public struct NavigationBarStyleConfiguration {
  /// A view that describes the content of a the OGNavigationBar.
  public let content: NavigationBarStyleConfiguration.Content

  /// The type-erased content of a OGNavigationBar.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public var body: AnyView
  }
}
