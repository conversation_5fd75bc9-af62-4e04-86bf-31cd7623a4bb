# OGNetworkLogger

The `OGNetworkLogger` is a framework that provides a set of tools to log, save and inspect `URLRequests` and `URLSessions`.

- [OGNetworkLogger](#ognetworklogger)
  - [Overview](#overview)
  - [Key Features](#key-features)
  - [Examples](#examples)
    - [Using the OGNetworkLogger](#using-the-ognetworklogger)
    - [Reading Requests](#reading-requests)
    - [Usage for WKWebViews](#usage-for-wkwebviews)

## Overview

The `OGNetworkLogger` framework leverages the power of `URLProtocol` and `URLSessionDelegate` to intercept and log network requests and responses. By registering a custom `URLProtocol`, the framework can capture all outgoing requests made through `URLSession`. It creates instances of `OGNetworkLoggerRequestModel` to store and represent each request, including its method, headers, and body.

When a request is initiated, the framework starts tracking its progress and duration using timers. It logs the request details, including headers and body, based on the specified logging options. Once the request is completed or encounters an error, the framework logs the response details, such as headers and body, if enabled.

Additionally, the framework offers `OGWebViewLoggingService` to facilitate logging requests made within `WKWebView`. By utilizing the `WKNavigationDelegate` methods, you can start and finish logging requests seamlessly, capturing their corresponding responses or errors.

## Key Features

- Request/Response logging: Capture and log detailed information about `URLRequests`, including request method, headers, and body.
- Request storage: Save logged requests for future reference and analysis.
- Integration with `WKWebView`: Enable logging for requests made within `WKWebView`.

## Examples

### Using the OGNetworkLogger

To start logging just use this simple one liner.

```Swift
OGNetworkLoggerContainer.shared.networkLogger().startLogger()
```

To stop logging just use this line of code.

```Swift
OGNetworkLoggerContainer.shared.networkLogger().stopLogger()
```

### Reading Requests

To read the requests you need the `OGNetworkLogger`. The `OGNetworkLogger` publishes an array of `OGNetworkLoggerRequestModel`, as soon as a requests gets saved. 

```Swift 
class <YourLogReceiver> {
  @OGInjected(\OGNetworkLoggerContainer.networkLogger) private var logger
  private var cancellables: Set<AnyCancellable> = []

  init() {
    logger.requestPublisher.sink { requests in
      // Read and inspect your requests here
      print(requests)
    }.store(in: &cancellables)
  }
}
```

### Usage for WKWebViews

As the `WKWebView` is not directly using URLSession under the hood, we need to manually log the requests. For this reason we implemented a helper class that takes care of all that. Simply integrate it into the `WKNavigationDelegate`. 

```Swift
// import the OGNetworkLogger
import OGNetworkLogger

class <YourNavigationDelegate>: WKNavigationDelegate {

    let webViewLoggingService = OGWebViewLoggingService()

    init() {}

    func webView(_ webView: WKWebView, decidePolicyFor navigationAction: WKNavigationAction, preferences: WKWebpagePreferences) async -> (WKNavigationActionPolicy, WKWebpagePreferences) {
      // If you're allowing the request, you should start logging it.
      webViewLoggingService.startLoggingRequest(navigationAction.request as NSURLRequest)
      return (.allow, preferences)
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation, withError error: Error) {
      // Finish the logging of the request with an error
      webViewLoggingService.finishLoggingRequest(withError: error)
    }

    func webView(_ webView: WKWebView, decidePolicyFor navigationResponse: WKNavigationResponse, decisionHandler: @escaping (WKNavigationResponsePolicy) -> Void) {
      // Finish the logging of the request with a response
      webViewLoggingService.finishLoggingRequest(withResponse: navigationResponse.response)
    
      decisionHandler(.allow)
    }

    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation, withError error: Error) {
      // Finish the logging of the request with an error
      webViewLoggingService.finishLoggingRequest(withError: error)
    }

    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
      // Finish the logging of the request without any further information
      webViewLoggingService.finishRequest()
    }
}