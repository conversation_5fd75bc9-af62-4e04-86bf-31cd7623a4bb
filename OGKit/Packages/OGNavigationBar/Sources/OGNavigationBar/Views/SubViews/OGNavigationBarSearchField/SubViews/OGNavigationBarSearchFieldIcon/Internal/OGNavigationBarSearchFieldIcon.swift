// swiftlint:disable type_name

import SwiftUI

// MARK: - OGNavigationBarSearchFieldIcon

struct OGNavigationBarSearchFieldIcon: View {
  @Environment(\.styleOGNavigationBarSearchFieldIcon) private var style

  init() {}

  var body: some View {
    style.makeBody(
      configuration: OGNavigationBarSearchFieldIconStyleConfiguration(
        content: OGNavigationBarSearchFieldIconStyleConfiguration.Content(
          content: content
        )
      )
    )
  }

  var content: some View {
    Image(systemName: "magnifyingglass")
  }
}

// MARK: - OGNavigationBarSearchFieldIcon_Previews

struct OGNavigationBarSearchFieldIcon_Previews: PreviewProvider {
  static var previews: some View {
    OGNavigationBarSearchFieldIcon()
  }
}
