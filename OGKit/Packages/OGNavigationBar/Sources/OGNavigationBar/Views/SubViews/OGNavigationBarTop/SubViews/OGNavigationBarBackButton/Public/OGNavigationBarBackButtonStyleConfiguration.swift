// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGNavigationBarBackButton.
public struct OGNavigationBarBackButtonStyleConfiguration {
  /// A view that describes the content of a the OGNavigationBarBackButton.
  public let content: OGNavigationBarBackButtonStyleConfiguration.Content

  /// The type-erased content of a OGNavigationBarBackButton.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public private(set) var body: AnyView
  }
}
