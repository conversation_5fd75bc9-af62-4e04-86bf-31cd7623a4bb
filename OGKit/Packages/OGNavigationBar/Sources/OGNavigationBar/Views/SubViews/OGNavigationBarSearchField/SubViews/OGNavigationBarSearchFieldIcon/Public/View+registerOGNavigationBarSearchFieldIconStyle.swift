import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGNavigationBarSearchFieldIconStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGNavigationBarSearchFieldIcon instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGNavigationBarSearchFieldIconStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGNavigationBarSearchFieldIconStyle``.
  /// - Returns: A view that represents a OGNavigationBarSearchFieldIcon.
  @ViewBuilder
  public func register(_ style: some OGNavigationBarSearchFieldIconStyle) -> some View {
    environment(\.styleOGNavigationBarSearchFieldIcon, AnyOGNavigationBarSearchFieldIconStyle(style: style))
  }
}
