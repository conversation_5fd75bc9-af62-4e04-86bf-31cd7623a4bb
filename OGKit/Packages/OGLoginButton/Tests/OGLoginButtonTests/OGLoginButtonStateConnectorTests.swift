import OGCoreTestsUtils
import XCTest

@testable import OGLoginButton

final class OGLoginButtonStateConnectorTests: XCTestCase {
  func test_WHEN_configure_THEN_updatedNavigationTitleConfig() async throws {
    await withMainSerialExecutor {
      let expectation = expectation(description: "Expected set event to be received")

      let sut = OGLoginButtonState.Connector()
      var actualEvents: [OGLoginButtonAction] = []

      let dispatch = { event in
        actualEvents.append(event)
        if actualEvents.count == 1 {
          expectation.fulfill()
        }
      }

      await sut.configure(dispatch: dispatch)

      await fulfillment(of: [expectation], timeout: 0.1)

      let expectedEvents: [OGLoginButtonAction] = [
        ._updatedNavigationTitleConfig(
          LoginNavigationTitleButton(
            isEnabled: false,
            enabledUrls: []
          )
        )
      ]
      XCTAssertEqual(actualEvents, expectedEvents)
    }
  }
}
