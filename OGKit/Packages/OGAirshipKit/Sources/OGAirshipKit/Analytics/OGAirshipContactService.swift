import AirshipCore
import Foundation

// MARK: - TagGroupsEditing

public protocol TagGroupsEditing {
  func set(_ tags: [String], group: String)
}

// MARK: - TagGroupsEditor + TagGroupsEditing

extension TagGroupsEditor: TagGroupsEditing {}

// MARK: - ContactEditing

public protocol ContactEditing {
  func identify(_ namedUserID: String)
}

// MARK: - AirshipContact + ContactEditing

extension AirshipContact: ContactEditing {}

// MARK: - OGAirshipContactAttributes

public struct OGAirshipContactAttributes {
  public let userID: String?
  public let attributes: [OGAirshipAttribute]
  public let tags: [OGAirshipAttribute]
  public init(userID: String?, attributes: [OGAirshipAttribute], tags: [OGAirshipAttribute]) {
    self.userID = userID
    self.attributes = attributes
    self.tags = tags
  }
}

// MARK: - OGAirshipContactServing

public protocol OGAirshipContactServing {
  func resetContact()
  func setAttributes(_ attribute: [OGAirshipAttribute])
  func setTagGroups(_ attribute: [OGAirshipAttribute])
  func setContact(_ value: String?)
}

// MARK: - OGAirshipContactService

public struct OGAirshipContactService: OGAirshipContactServing {
  private let attributesEditor: AttributesEditing
  private let tagsEditor: TagGroupsEditing
  private let contact: ContactEditing
  private let resetContactBlock: () -> Void

  public init(
    attributesEditor: AttributesEditing = Airship.contact.editAttributes(),
    contact: ContactEditing = Airship.contact,
    resetContact: @escaping () -> Void = Airship.contact.reset,
    tagsEditor: TagGroupsEditing = Airship.contact.editTagGroups()
  ) {
    self.attributesEditor = attributesEditor
    self.tagsEditor = tagsEditor
    self.contact = contact
    self.resetContactBlock = resetContact
  }

  public func resetContact() {
    resetContactBlock()
  }

  public func setContact(_ value: String?) {
    guard let value else { return }
    contact.identify(value)
  }

  public func setAttributes(_ attributes: [OGAirshipAttribute]) {
    attributes.forEach {
      setAttribute($0)
    }
  }

  private func setAttribute(_ attribute: OGAirshipAttribute) {
    set(attributes: attribute.toDictionary())
  }

  public func setTagGroups(_ attribute: [OGAirshipAttribute]) {
    attribute
      .forEach {
        if let value = $0.value as? [String] {
          tagsEditor.set(value, group: $0.name)
        }
      }
  }

  private func set(attributes: [String: Any]) {
    attributes.forEach {
      if let string = $0.value as? String {
        if string.isEmpty {
          remove(keys: [$0.key])
        } else {
          attributesEditor.set(string: string, attribute: $0.key)
        }
      } else if let date = $0.value as? Date {
        attributesEditor.set(date: date, attribute: $0.key)
      } else if let int = $0.value as? Int {
        attributesEditor.set(int: int, attribute: $0.key)
      } else if let double = $0.value as? Double {
        attributesEditor.set(double: double, attribute: $0.key)
      }
    }
  }

  public func remove(keys: [String]) {
    keys.forEach {
      attributesEditor.remove($0)
    }
  }
}
