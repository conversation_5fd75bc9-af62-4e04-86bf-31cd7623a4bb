import AirshipMessageCenter
import Combine
import Foundation
import OGAirshipKit
import OGMacros
import <PERSON>GMock

@OGMock
public final class MessageCenterInboxMock: AirshipInboxServing {
  public var messagePublisher: AnyPublisher<[MessageCenterMessage], Never> = Just([]).eraseToAnyPublisher()

  public var unreadCountPublisher: AnyPublisher<Int, Never> = Just(0).eraseToAnyPublisher()

  public init() {}

  public func refreshMessages() async -> Bool {
    await mock.refreshMessages()
  }

  public func markRead(messageIDs: [String]) async {
    await mock.markRead(messageIDs: messageIDs)
  }

  public func delete(messageIDs: [String]) async {
    await mock.delete(messageIDs: messageIDs)
  }
}
