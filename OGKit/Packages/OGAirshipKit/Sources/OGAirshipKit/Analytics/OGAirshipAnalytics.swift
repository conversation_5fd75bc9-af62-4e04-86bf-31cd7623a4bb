import AirshipCore
import Combine
import Foundation
import OGCore
import OGDIService

// MARK: - AirshipEventReportable

public protocol AirshipEventReportable {
  var mappingId: String { get }
  var attribute: OGAirshipAttribute? { get }
  var contact: OGAirshipContactAttributes? { get }
  var customEvent: CustomEvent? { get }
}

// MARK: - OGAirshipAnalyticsLogging

/// Protocol that exposes how to log an event into Firebase Analytics.
public protocol OGAirshipAnalyticsLogging {
  func logEvent(_: any AirshipEventReportable)
}

// MARK: - OGAirshipAnalytics

/// Firebase Analytics proxy wrapper.
public struct OGAirshipAnalytics: OGAirshipAnalyticsLogging {
  @OGInjected(\OGCoreContainer.logger) private var logger

  private let airshipChannelService: OGAirshipChannelServing?
  private let airshipContactService: OGAirshipContactServing?

  private var cancellables = Set<AnyCancellable>()

  public init(
    airshipChannelService: OGAirshipChannelServing? = OGAirshipContainer.shared.channelService(),
    airshipContactService: OGAirshipContactServing? = OGAirshipContainer.shared.contactService()
  ) {
    self.airshipChannelService = airshipChannelService
    self.airshipContactService = airshipContactService
  }

  public func logEvent(_ event: any AirshipEventReportable) {
    if let attribute = event.attribute {
      airshipChannelService?.setAttribute(attribute)
      logger.log(.debug, domain: .tracking, message: "Airship channel: \(attribute.name) tracked with \(attribute.value)")
    } else if let customEvent = event.customEvent {
      customEvent.track()
      logger.log(
        .debug,
        domain: .tracking,
        message: "Airship event: \(customEvent.eventName ?? "NAN") tracked with eventType: \(customEvent.eventType), eventValue: \(customEvent.eventValue ?? NSNumber(value: 0)), transactionID: \(customEvent.transactionID ?? "NAN")"
      )
    } else if let contact = event.contact {
      airshipContactService?.setContact(contact.userID)
      airshipContactService?.setAttributes(contact.attributes)
      airshipContactService?.setTagGroups(contact.tags)
      logger.log(
        .debug,
        domain: .tracking,
        message: "Airship userID: \(contact.userID ?? "NAN") tracked with attributes: \(contact.attributes.map { "\($0.name):\($0.value)" }.joined(separator: "\n")), tags: \(contact.tags.map { "\($0.name):\($0.value)" }.joined(separator: "\n"))"
      )
    } else {
      logger.log(.debug, domain: .tracking, message: "Airship event is invalid")
    }
  }
}
