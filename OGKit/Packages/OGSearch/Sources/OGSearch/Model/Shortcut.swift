import Foundation
import OGCore
import OGL10n
import OGTenantKit

// MARK: - Shortcut

public struct Shortcut: Codable, Identifiable, Equatable, Sendable {
  public let id = UUID()
  public let iconName: String?
  public let iconUrl: URL?
  public let label: String
  public let l10n: String?
  public let url: String

  public var resolvedL10n: String {
    guard let l10n else {
      return label
    }
    return ogL10n.resolve(key: l10n)
  }

  public init(
    iconName: String?,
    iconUrl: URL?,
    l10n: String? = nil,
    label: String,
    url: String
  ) {
    self.iconName = iconName
    self.iconUrl = iconUrl
    self.l10n = l10n
    self.label = label
    self.url = url
  }

  public init(from decoder: any Decoder) throws {
    let container = try decoder.container(keyedBy: CodingKeys.self)
    self.iconName = try container.decodeIfPresent(String.self, forKey: .iconName)
    self.iconUrl = try container.decodeIfPresent(URL.self, forKey: .iconUrl)
    self.l10n = try container.decodeIfPresent(String.self, forKey: .l10n)
    if let l10n {
      self.label = ogL10n.resolve(key: l10n)
    } else {
      self.label = try container.decode(String.self, forKey: .label)
    }
    self.url = try container.decode(String.self, forKey: .url)
  }
}

// MARK: Hashable

extension Shortcut: Hashable {
  public func hash(into hasher: inout Hasher) {
    hasher.combine(iconName)
    hasher.combine(iconUrl)
    hasher.combine(l10n)
    hasher.combine(label)
    hasher.combine(url)
  }
}
