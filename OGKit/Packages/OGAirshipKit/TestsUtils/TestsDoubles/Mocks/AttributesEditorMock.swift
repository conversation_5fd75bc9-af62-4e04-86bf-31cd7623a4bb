import OGAirshipKit

public final class AttributesEditorMock: AttributesEditing {
  public init() {}

  public func remove(_: String) {}
  public func set(date _: Date, attribute _: String) {}
  public func set(double _: Double, attribute _: String) {}
  public func set(int _: Int, attribute _: String) {}
  public func set(string _: String, attribute _: String) {}
  public func set(float _: Float, attribute _: String) {}
  public func set(uint _: UInt, attribute _: String) {}
  public func apply() {}
}
