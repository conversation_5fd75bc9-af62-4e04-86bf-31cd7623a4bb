import Foundation
import OGCore
import OGDIService
import UIKit.UIDevice

// MARK: - OGUserAgentProvidable

public protocol OGUserAgentProvidable {
  func update() -> String
}

// MARK: - OGUserAgent

public struct OGUserAgent: OGUserAgentProvidable {
  @OGInjected(\OGUserAgentFeatureAdapterContainer.config) private var config
  @OGInjected(\OGCoreContainer.appEnvironment) private var appEnvironment
  @OGInjected(\OGUserAgentContainer.dynamicValuesProvider) private var dynamicValuesProvider

  public enum Keys: String {
    case app = "App"
    case appType = "AppType"
    case osVersion
    case model = "Model"
    case appName
    case appID = "appId"
    case appVersion = "AppVersion"
    case idfa = "App_IDfA"
    case adjustID = "App_adjust_adid"
    case airshipID = "App_airship_channel_id"
  }

  public func update() -> String {
    createUserAgent()
  }

  private func createUserAgent() -> String {
    var userAgentValues = appValues()
    userAgentValues = userAgentValues.merging(infoPlistValues()) { _, new in new }
    if config.isAdjustEnabled.value {
      userAgentValues = userAgentValues.merging(adjustValues()) { _, new in new }
    }
    if config.isAirshipEnabled.value {
      userAgentValues = userAgentValues.merging(airshipValues()) { _, new in new }
    }
    if config.isAdvertisingIdEnabled.value {
      userAgentValues = userAgentValues.merging(advertisingIdentifierValues()) { _, new in new }
    }

    let customUserAgent = userAgentValues.sorted(by: { $0.key.rawValue < $1.key.rawValue }).map { "\($0.rawValue)=\($1)" }

    return "[\(customUserAgent.joined(separator: ";"))]"
  }

  private func appValues() -> [Keys: String] {
    var userAgentValues = [Keys: String]()
    userAgentValues[.app] = "true"
    userAgentValues[.appType] = "iOS"
    userAgentValues[.osVersion] = appEnvironment.systemVersion
    userAgentValues[.model] = appEnvironment.deviceModel

    return userAgentValues
  }

  private func infoPlistValues() -> [Keys: String] {
    var userAgentValues = [Keys: String]()

    if let appName = appEnvironment.appName {
      userAgentValues[.appName] = appName
    }

    if let appId = appEnvironment.appId {
      userAgentValues[.appID] = appId
    }

    if let appVersion = appEnvironment.version {
      userAgentValues[.appVersion] = appVersion
    }

    return userAgentValues
  }

  private func advertisingIdentifierValues() -> [Keys: String] {
    var userAgentValues = [Keys: String]()
    if let idfa = appEnvironment.advertisingIdentifier {
      userAgentValues[.idfa] = idfa
    }
    return userAgentValues
  }

  private func adjustValues() -> [Keys: String] {
    var userAgentValues = [Keys: String]()
    if let identifier = dynamicValuesProvider.adjustId?() {
      userAgentValues[.adjustID] = identifier
    }

    return userAgentValues
  }

  private func airshipValues() -> [Keys: String] {
    var userAgentValues = [Keys: String]()
    if let identifier = dynamicValuesProvider.airshipChannelId?() {
      userAgentValues[.airshipID] = identifier
    }
    return userAgentValues
  }
}
