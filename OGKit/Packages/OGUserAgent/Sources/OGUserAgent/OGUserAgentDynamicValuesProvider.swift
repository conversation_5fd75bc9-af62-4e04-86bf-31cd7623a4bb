import Foundation

// MARK: - OGUserAgentDynamicValuesProvidable

public protocol OGUserAgentDynamicValuesProvidable {
  var airshipChannelId: (() -> String?)? { get set }
  var adjustId: (() -> String?)? { get set }
}

// MARK: - OGUserAgentDynamicValuesProvider

public final class OGUserAgentDynamicValuesProvider: OGUserAgentDynamicValuesProvidable {
  public var airshipChannelId: (() -> String?)?
  public var adjustId: (() -> String?)?
}
