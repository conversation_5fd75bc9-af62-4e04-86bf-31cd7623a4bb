import AirshipCore
import AirshipMessageCenter
import Combine
import Foundation
import OGCore
import OGDIService

// MARK: - OGAirshipInboxServing

public protocol OGAirshipInboxServing {
  func deleteMessages(with ids: [String]) async
  func deleteMessage(with id: String) async
  func markRead(messageId: String) async
  func refreshMessages() async -> Bool
  func watchMessages() -> AnyPublisher<[OGAirshipInboxMessage], Never>
  func watchUnreadCount() -> AnyPublisher<Int, Never>
}

// MARK: - AirshipInboxServing

public protocol AirshipInboxServing {
  var messagePublisher: AnyPublisher<[MessageCenterMessage], Never> { get }
  var unreadCountPublisher: AnyPublisher<Int, Never> { get }
  func delete(messageIDs: [String]) async
  func markRead(messageIDs: [String]) async
  func refreshMessages() async -> Bool
}

// MARK: - MessageCenterInbox + AirshipInboxServing

extension MessageCenterInbox: AirshipInboxServing {}

// MARK: - OGAirshipInboxService

public actor OGAirshipInboxService: OGAirshipInboxServing {
  private let inbox: AirshipInboxServing

  public init(
    inbox: AirshipInboxServing = MessageCenter.shared.inbox
  ) {
    self.inbox = inbox
  }

  public func deleteMessages(with ids: [String]) async {
    await inbox.delete(messageIDs: ids)
  }

  public func deleteMessage(with id: String) async {
    await inbox.delete(messageIDs: [id])
  }

  public func markRead(messageId: String) async {
    await inbox.markRead(messageIDs: [messageId])
  }

  public func refreshMessages() async -> Bool {
    await inbox
      .refreshMessages()
  }

  public nonisolated func watchMessages() -> AnyPublisher<[OGAirshipInboxMessage], Never> {
    inbox
      .messagePublisher
      .receive(on: RunLoop.main)
      .map(\.toMessages)
      .eraseToAnyPublisher()
  }

  public nonisolated func watchUnreadCount() -> AnyPublisher<Int, Never> {
    inbox.unreadCountPublisher
  }
}
