import OGAirshipKit

extension AirshipTrackingFeatureConfigurable {
  public static var stub: any AirshipTrackingFeatureConfigurable {
    AirshipTrackingFeatureConfiguration.stub
  }
}

extension AirshipTrackingFeatureConfiguration {
  public static var stub: AirshipTrackingFeatureConfiguration {
    AirshipTrackingFeatureConfiguration()
  }
}

// MARK: - AirshipTrackingFeatureConfiguration

public struct AirshipTrackingFeatureConfiguration: AirshipTrackingFeatureConfigurable {
  public var attributeIdMapping: [String: String]
  public var eventIdMapping: [String: Bool]
  public var isEnabled: Bool
  public var keepContactAssociation: Bool

  public init(
    attributeIdMapping: [String: String] = [:],
    eventIdMapping: [String: Bool] = [:],
    isEnabled: Bool = true,
    keepContactAssociation: Bool = true
  ) {
    self.attributeIdMapping = attributeIdMapping
    self.eventIdMapping = eventIdMapping
    self.isEnabled = isEnabled
    self.keepContactAssociation = keepContactAssociation
  }
}
