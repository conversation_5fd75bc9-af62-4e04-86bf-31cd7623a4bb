// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGNavigationBarBackButtonIcon.
public struct OGNavigationBarBackButtonIconStyleConfiguration {
  /// A view that describes the content of a the OGNavigationBarBackButtonIcon.
  public let content: OGNavigationBarBackButtonIconStyleConfiguration.Content

  /// The type-erased content of a OGNavigationBarBackButtonIcon.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public private(set) var body: AnyView
  }
}
