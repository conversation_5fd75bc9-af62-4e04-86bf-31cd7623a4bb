// swiftlint:disable type_name

import SwiftUI

// MARK: - OGNavigationBarBackButtonStyleKey

struct OGNavigationBarBackButtonStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGNavigationBarBackButtonStyle(style: DefaultOGNavigationBarBackButtonStyle())
}

extension EnvironmentValues {
  var styleOGNavigationBarBackButton: AnyOGNavigationBarBackButtonStyle {
    get { self[OGNavigationBarBackButtonStyleKey.self] }
    set { self[OGNavigationBarBackButtonStyleKey.self] = newValue }
  }
}
