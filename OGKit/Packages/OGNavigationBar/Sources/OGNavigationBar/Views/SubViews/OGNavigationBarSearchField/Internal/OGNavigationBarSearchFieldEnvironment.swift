// swiftlint:disable type_name

import SwiftUI

// MARK: - OGNavigationBarSearchFieldStyleKey

struct OGNavigationBarSearchFieldStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGNavigationBarSearchFieldStyle(style: DefaultOGNavigationBarSearchFieldStyle())
}

extension EnvironmentValues {
  var styleOGNavigationBarSearchField: AnyOGNavigationBarSearchFieldStyle {
    get { self[OGNavigationBarSearchFieldStyleKey.self] }
    set { self[OGNavigationBarSearchFieldStyleKey.self] = newValue }
  }
}
