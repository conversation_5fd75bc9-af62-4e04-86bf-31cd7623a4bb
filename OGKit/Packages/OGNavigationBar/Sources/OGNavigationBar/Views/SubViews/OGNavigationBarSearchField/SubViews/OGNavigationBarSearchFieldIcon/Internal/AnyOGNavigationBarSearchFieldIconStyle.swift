// swiftlint:disable type_name

import SwiftUI

struct AnyOGNavigationBarSearchFieldIconStyle: OGNavigationBarSearchFieldIconStyle {
  private var _makeBody: (Configuration) -> AnyView

  init(style: some OGNavigationBarSearchFieldIconStyle) {
    self._makeBody = { configuration in
      AnyView(style.makeBody(configuration: configuration))
    }
  }

  func makeBody(configuration: Configuration) -> some View {
    _makeBody(configuration)
  }
}
