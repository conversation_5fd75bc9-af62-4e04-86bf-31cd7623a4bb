// Generated using OGFeatureAdapterMaker
// swiftlint:disable all
import Combine
import Foundation
import OGCore
import OGDIService
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import OGMacros
import OGTenantKit

// MARK: - SearchFeatureConfigurable

public protocol SearchFeatureConfigurable {
  var isEnabled: Bool { get set }

  var debounceMillis: Int { get set }
  var maxHistoryItems: Int { get set }
  var minCharacters: Int { get set }
  var secretIdentifier: String { get set }
  var suggestionsApiUrl: String { get set }
  var supportedUrls: [URL] { get set }
  var webPath: String { get set }
  var shortcuts: [Shortcut] { get set }
}

// MARK: - SearchFeatureAdapter

public final class SearchFeatureAdapter: OGFeatureAdapter, SearchFeatureAdaptable {
  override public class var featureName: OGFeature.Name { OGIdentifier.search.value }

  public let configuration: CurrentValueSubject<SearchFeatureConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()

  public init(configuration: SearchFeatureConfigurable?) {
    guard let configuration else {
      fatalError("The SearchFeatureConfiguration has not been registered")
    }
    self.configuration = CurrentValueSubject(configuration)
    super.init()

    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature.sink { [weak self] feature in
      guard let self else { return }
      var updatedConfiguration = self.configuration.value
      guard let feature else {
        updatedConfiguration.isEnabled = false
        self.configuration.send(updatedConfiguration)
        return
      }
      updatedConfiguration.isEnabled = feature.isEnabled

      let debounceMillis: Int = (feature.customValue(for: OGFeatureKey.CustomValues.Search.debounceMillis)) ?? self.configuration.value.debounceMillis
      updatedConfiguration.debounceMillis = debounceMillis
      let maxHistoryItems: Int = (feature.customValue(for: OGFeatureKey.CustomValues.Search.maxHistoryItems)) ?? self.configuration.value.maxHistoryItems
      updatedConfiguration.maxHistoryItems = maxHistoryItems
      let minCharacters: Int = (feature.customValue(for: OGFeatureKey.CustomValues.Search.minCharacters)) ?? self.configuration.value.minCharacters
      updatedConfiguration.minCharacters = minCharacters
      let secretIdentifier: String = (feature.customValue(for: OGFeatureKey.CustomValues.Search.secretIdentifier)) ?? self.configuration.value.secretIdentifier
      updatedConfiguration.secretIdentifier = secretIdentifier
      let suggestionsApiUrl: String = (feature.customValue(for: OGFeatureKey.CustomValues.Search.suggestionsApiUrl)) ?? self.configuration.value.suggestionsApiUrl
      updatedConfiguration.suggestionsApiUrl = suggestionsApiUrl
      let supportedUrls: [URL] = (feature.customValue(for: OGFeatureKey.CustomValues.Search.supportedUrls)) ?? self.configuration.value.supportedUrls
      updatedConfiguration.supportedUrls = supportedUrls
      let webPath: String = (feature.customValue(for: OGFeatureKey.CustomValues.Search.webPath)) ?? self.configuration.value.webPath
      updatedConfiguration.webPath = webPath
      var shortcuts = [Shortcut]()
      do {
        if
          let tempShortcuts = try feature.customValue(
            [Shortcut].self,
            for: OGFeatureKey.CustomValues.Search.shortcuts
          ) {
          shortcuts = tempShortcuts
        }
      } catch {
        logger.critical(domain: .decoding, message: error.localizedDescription)
      }
      updatedConfiguration.shortcuts = shortcuts

      self.configuration.send(updatedConfiguration)
    }.store(in: &subscriptions)
  }
}

// MARK: - SearchFeatureAdaptable

public protocol SearchFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<SearchFeatureConfigurable, Never> { get }
}

// MARK: - OGFeatureKey.CustomValues.Search

extension OGFeatureKey.CustomValues {
  public enum Search: String, OGKeyReceivable {
    public var value: String {
      rawValue
    }

    case debounceMillis
    case maxHistoryItems
    case minCharacters
    case secretIdentifier
    case suggestionsApiUrl
    case supportedUrls
    case webPath
    case shortcuts
  }
}

extension OGIdentifier {
  public static let search = #identifier("search")
}
