import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGNavigationBarSearchFieldStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGNavigationBarSearchField instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGNavigationBarSearchFieldStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGNavigationBarSearchFieldStyle``.
  /// - Returns: A view that represents a OGNavigationBarSearchField.
  @ViewBuilder
  public func register(_ style: some OGNavigationBarSearchFieldStyle) -> some View {
    environment(\.styleOGNavigationBarSearchField, AnyOGNavigationBarSearchFieldStyle(style: style))
  }
}
