import SwiftUI

// MARK: - OGCircleLoadingView

public struct OGCircleLoadingView: View {
  @Environment(\.styleOGCircleLoadingView) private var style

  public init() {}

  public var body: some View {
    style.makeBody(configuration: OGCircleLoadingViewStyleConfiguration(
      content: OGCircleLoadingViewStyleConfiguration.Content(content: content)
    ))
  }

  private var content: some View {
    EmptyView()
  }
}

// MARK: - OGCircleLoadingView_Previews

struct OGCircleLoadingView_Previews: PreviewProvider {
  static var previews: some View {
    OGCircleLoadingView()
  }
}
