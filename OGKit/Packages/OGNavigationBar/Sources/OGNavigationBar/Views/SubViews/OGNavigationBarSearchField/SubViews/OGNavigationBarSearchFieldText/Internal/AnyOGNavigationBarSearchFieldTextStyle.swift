// swiftlint:disable type_name

import SwiftUI

struct AnyOGNavigationBarSearchFieldTextStyle: OGNavigationBarSearchFieldTextStyle {
  private var _makeBody: (Configuration) -> AnyView

  init(style: some OGNavigationBarSearchFieldTextStyle) {
    self._makeBody = { configuration in
      AnyView(style.makeBody(configuration: configuration))
    }
  }

  func makeBody(configuration: Configuration) -> some View {
    _makeBody(configuration)
  }
}
