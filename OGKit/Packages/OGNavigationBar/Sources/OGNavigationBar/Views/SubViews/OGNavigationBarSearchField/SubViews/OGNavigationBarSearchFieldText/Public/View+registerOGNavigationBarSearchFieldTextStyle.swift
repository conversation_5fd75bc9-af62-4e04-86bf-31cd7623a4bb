import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGNavigationBarSearchFieldTextStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGNavigationBarSearchFieldText instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGNavigationBarSearchFieldTextStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGNavigationBarSearchFieldTextStyle``.
  /// - Returns: A view that represents a OGNavigationBarSearchFieldText.
  @ViewBuilder
  public func register(_ style: some OGNavigationBarSearchFieldTextStyle) -> some View {
    environment(\.styleOGNavigationBarSearchFieldText, AnyOGNavigationBarSearchFieldTextStyle(style: style))
  }
}
