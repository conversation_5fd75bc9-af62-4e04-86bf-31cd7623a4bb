// swiftlint:disable type_name

import SwiftUI

// MARK: - OGLoginHeaderView

public struct OGLoginHeaderView: View {
  @Environment(\.styleOGLoginHeaderView) private var style

  public init() {}

  public var body: some View {
    style.makeBody(configuration: OGLoginHeaderViewStyleConfiguration(
      content: OGLoginHeaderViewStyleConfiguration.Content(content: content)))
  }

  private var content: some View {
    EmptyView()
  }
}

// MARK: - OGLoginHeaderView_Previews

struct OGLoginHeaderView_Previews: PreviewProvider {
  static var previews: some View {
    OGLoginHeaderView()
  }
}
