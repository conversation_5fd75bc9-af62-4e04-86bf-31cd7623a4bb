import AirshipCore
import NotificationCenter
@testable import OGAirshipKit
import OGAirshipKitTestsUtils
import XCTest

final class OGAirshipPushSetupTest: XCTestCase {
  func test_GIVEN_airshipPush_THAN_airshipPushIsConfigured() throws {
    let airshipPushMock = OGAirshipPushMock()
    let sut = OGAirshipPushSetup {
      airshipPushMock
    }
    let airshipPushServiceMock = OGAirshipPushServiceMock()
    sut.setupPush(with: airshipPushServiceMock)

    XCTAssertEqual(airshipPushMock.mock.defaultPresentationOptions.setter.callsCount, 1)
    XCTAssertEqual(airshipPushMock.mock.notificationOptions.setter.callsCount, 1)
    XCTAssertEqual(airshipPushMock.mock.autobadgeEnabled.setter.callsCount, 1)
    XCTAssertEqual(airshipPushMock.mock.pushNotificationDelegate.setter.callsCount, 1)
    XCTAssertEqual(airshipPushMock.mock.registrationDelegate.setter.callsCount, 1)
    XCTAssertEqual(airshipPushMock.mock.updateRegistrationCalls.callsCount, 1)
    XCTAssertEqual(airshipPushMock.mock.defaultPresentationOptions.setter.latestCall, [.banner, .badge, .sound])
    XCTAssertEqual(airshipPushMock.mock.notificationOptions.setter.latestCall, [.provisional, .alert, .badge, .sound, .carPlay])
    XCTAssertEqual(airshipPushMock.mock.userPushNotificationsEnabled.setter.latestCall, true)
    XCTAssertEqual(airshipPushMock.mock.autobadgeEnabled.setter.latestCall, true)
    XCTAssertNotNil(airshipPushMock.mock.pushNotificationDelegate.setter.latestCall as Any?)
    XCTAssertNotNil(airshipPushMock.mock.registrationDelegate.setter.latestCall as Any?)
  }
}
