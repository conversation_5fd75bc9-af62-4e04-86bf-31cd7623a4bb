// swiftlint:disable type_name

import SwiftUI

// MARK: - OGNavigationBarSearchButtonStyleKey

struct OGNavigationBarSearchButtonStyleKey: EnvironmentKey {
  static var defaultValue = AnyOGNavigationBarSearchButtonStyle(style: DefaultOGNavigationBarSearchButtonStyle())
}

extension EnvironmentValues {
  var styleOGNavigationBarSearchButton: AnyOGNavigationBarSearchButtonStyle {
    get { self[OGNavigationBarSearchButtonStyleKey.self] }
    set { self[OGNavigationBarSearchButtonStyleKey.self] = newValue }
  }
}
