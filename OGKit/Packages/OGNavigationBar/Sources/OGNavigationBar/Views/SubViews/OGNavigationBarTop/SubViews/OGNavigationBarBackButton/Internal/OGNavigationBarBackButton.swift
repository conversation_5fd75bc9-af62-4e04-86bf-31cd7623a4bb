import OGDIService
import OGNavigationCore
import SwiftUI

// MARK: - OGNavigationBarBackButton

struct OGNavigationBarBackButton: View {
  @AccessibilityFocusState private var isTitleFocused: Bool

  @Environment(\.styleOGNavigationBarBackButton) private var style
  @Environment(\.dismiss) private var dismiss
  @StateObject private var viewStore = Self.make()

  @OGInjected(\OGNavigationCoreContainer.backTap) var backTap

  init() {}

  var body: some View {
    style.makeBody(
      configuration: OGNavigationBarBackButtonStyleConfiguration(
        content: OGNavigationBarBackButtonStyleConfiguration.Content(
          content: content
        )
      )
    )
  }

  @ViewBuilder private var content: some View {
    if viewStore.canGoBack {
      Button(
        action: {
          backTap.didTap()
          dismiss()
        },
        label: {
          OGNavigationBarBackButtonIcon()
        }
      )
      .accessibilityFocused($isTitleFocused)
      .task {
        isTitleFocused = true
      }
    }
  }
}

// MARK: - OGNavigationBarBackButton_Previews

struct OGNavigationBarBackButton_Previews: PreviewProvider {
  static var previews: some View {
    OGNavigationBarBackButton()
  }
}
