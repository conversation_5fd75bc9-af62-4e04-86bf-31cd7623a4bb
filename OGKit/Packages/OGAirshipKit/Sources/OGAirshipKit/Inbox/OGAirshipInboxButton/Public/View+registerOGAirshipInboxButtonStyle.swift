import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGAirshipInboxButtonStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGAirshipInboxButton instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGAirshipInboxButtonStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGAirshipInboxButtonStyle``.
  /// - Returns: A view that represents a OGAirshipInboxButton.
  @ViewBuilder
  public func register(_ style: some OGAirshipInboxButtonStyle) -> some View {
    environment(\.styleOGAirshipInboxButton, AnyOGAirshipInboxButtonStyle(style: style))
  }
}
