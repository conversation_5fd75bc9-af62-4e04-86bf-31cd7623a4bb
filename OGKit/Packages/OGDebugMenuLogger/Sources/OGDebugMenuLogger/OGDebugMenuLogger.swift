import Combine
import Foundation
import OGCore
import OGDIService
import SwiftUI

// MARK: - DebugMenuDebuggable

public protocol DebugMenuDebuggable {
  func apiLogInformation() -> [(key: Date, value: String)]
  func deleteApiLogInformation()
  func stateLogInformation() -> [(key: Date, value: String)]
  func deleteLogInformation()
}

// MARK: - OGPersistenceKey.DebugMenuLogger

extension OGPersistenceKey {
  public enum DebugMenuLogger: String, RawValueable {
    case apiLogs
    case stateLogs
  }
}

// MARK: - DebugMenuLoggerable

public protocol DebugMenuLoggerable: OGLogReceivable, DebugMenuDebuggable {}

// MARK: - OGDebugMenuLogger

public final class OGDebugMenuLogger: DebugMenuLoggerable {
  typealias Logs = [Date: String]

  public let logDistributer: OGLogDistributable
  public var cancellables: Set<AnyCancellable>

  @OGInjected(\OGCoreContainer.storage) private var storage

  private var apiLogs = Logs()
  private var stateLogs = Logs()
  private var webbridgeLogs = Logs()
  private var serviceLogs = Logs()

  let identifier = "MENULOG"

  public func didReceiveLog(_ log: OGLog) {
    let now = Date()
    let message = log.message

    switch log.domain {
    case .api, .authentication, .decoding: apiLogs[now] = message
    case .state:
      stateLogs[now] = message
    case .webbridge:
      webbridgeLogs[now] = message
    case .service:
      serviceLogs[now] = message
    }
  }

  public func apiLogInformation() -> [(key: Date, value: String)] {
    apiLogs.map { (key: $0.key, value: $0.value) }
  }

  public func stateLogInformation() -> [(key: Date, value: String)] {
    stateLogs.map { (key: $0.key, value: $0.value) }
  }

  public func deleteApiLogInformation() {
    apiLogs.removeAll()
    storage.delete(valueForKey: OGPersistenceKey.DebugMenuLogger.apiLogs)
  }

  public func deleteLogInformation() {
    stateLogs.removeAll()
    storage.delete(valueForKey: OGPersistenceKey.DebugMenuLogger.stateLogs)
  }

  public init(logDistributable: OGLogDistributable) {
    self.logDistributer = logDistributable
    self.cancellables = []
    restoreApiLogs()
    restoreStateLogs()
    NotificationCenter.default.addObserver(forName: UIApplication.willTerminateNotification, object: nil, queue: .main) { [weak self] _ in
      self?.persistApiLogs()
      self?.persistStateLogs()
    }
    receiveAllLogs()
  }

  private func restoreApiLogs() {
    restoreLogs(.apiLogs)
  }

  private func persistApiLogs() {
    persist(logs: apiLogs, forKey: .apiLogs)
  }

  private func restoreStateLogs() {
    restoreLogs(.stateLogs)
  }

  private func persistStateLogs() {
    persist(logs: stateLogs, forKey: .stateLogs)
  }

  private func persist(logs: Logs, forKey key: OGPersistenceKey.DebugMenuLogger) {
    do {
      let encodedData = try NSKeyedArchiver.archivedData(withRootObject: logs, requiringSecureCoding: false)
      storage.persist(value: encodedData, forKey: key)
    } catch {
      // Error handling for archiving data
    }
  }

  private func restoreLogs(_ key: OGPersistenceKey.DebugMenuLogger) {
    do {
      let decodedData = (storage.value(forKey: key) as? Data) ?? Data()
      let wrappedLogs = try NSKeyedUnarchiver.unarchivedObject(ofClass: LogsWrapper.self, from: decodedData) ?? LogsWrapper.empty
      //			let logs = try NSKeyedUnarchiver.unarchivedDictionary(ofKeyClass: Date.self, objectClass: String.self, from: decodedData)
      //								as? [Date: String]) ?? [:]
      switch key {
      case .stateLogs:
        stateLogs = wrappedLogs.logs
      case .apiLogs:
        apiLogs = wrappedLogs.logs
      }
    } catch {
      // Error handling for unarchiving data
    }
  }
}

// MARK: - LogsWrapper

final class LogsWrapper: NSObject, NSCoding {
  static let empty = LogsWrapper(logs: [:])
  static var supportsSecureCoding = false

  private static let codingKey: String = "CodingKey.logs"

  let logs: [Date: String]

  init(logs: [Date: String]) {
    self.logs = logs
  }

  func encode(with coder: NSCoder) {
    coder.encode(logs, forKey: Self.codingKey)
  }

  init?(coder: NSCoder) {
    if let logs = coder.decodeObject(forKey: Self.codingKey) as? [Date: String] {
      self.logs = logs
    } else {
      return nil
    }
  }
}
