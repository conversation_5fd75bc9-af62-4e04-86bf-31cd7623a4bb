import OGAirshipKitTestsUtils
import XCTest

@testable import OGAirshipKit

final class OGAirshipInboxStoreMiddlewareTests: XCTestCase {
  func test_WHEN_deleteMessage_THEN_serviceCalled_AND_noNextAction() async throws {
    let inboxService = OGAirshipInboxServiceMock()
    let sut = OGAirshipInboxState.Middleware(inboxService: inboxService)
    let nextAction = try await sut.callAsFunction(
      action: .deleteMessage(with: .stubRead),
      for: .init(messages: .stub)
    )

    XCTAssertEqual(1, inboxService.mock.deleteMessageCalls.callsCount)
    XCTAssertEqual(.stubRead, inboxService.mock.deleteMessageCalls.latestCall)
    XCTAssertNil(nextAction)
  }

  func test_WHEN_markRead_THEN_serviceCalled_AND_noNextAction() async throws {
    let inboxService = OGAirshipInboxServiceMock()
    let sut = OGAirshipInboxState.Middleware(inboxService: inboxService)
    let nextAction = try await sut.callAsFunction(
      action: .markRead(messageId: .stubRead),
      for: .init(messages: .stub)
    )

    XCTAssertEqual(1, inboxService.mock.markReadCalls.callsCount)
    XCTAssertEqual(.stubRead, inboxService.mock.markReadCalls.latestCall)
    XCTAssertNil(nextAction)
  }

  func test_WHEN_refreshMessages_THEN_serviceCalled_AND_noNextAction() async throws {
    let inboxService = OGAirshipInboxServiceMock()
    inboxService.mock.refreshMessagesCalls.mockCall { _ in
      true
    }
    let sut = OGAirshipInboxState.Middleware(inboxService: inboxService)
    let nextAction = try await sut.callAsFunction(
      action: .refreshMessages,
      for: .initial
    )

    XCTAssertEqual(1, inboxService.mock.refreshMessagesCalls.callsCount)
    XCTAssertNil(nextAction)
  }

  func test_WHEN_setMessages_THEN_noNextAction() async throws {
    let inboxService = OGAirshipInboxServiceMock()
    let sut = OGAirshipInboxState.Middleware(inboxService: inboxService)
    let nextAction = try await sut.callAsFunction(
      action: ._set(messages: .stub, unreadCount: 1),
      for: .initial
    )
    XCTAssertNil(nextAction)
  }
}
