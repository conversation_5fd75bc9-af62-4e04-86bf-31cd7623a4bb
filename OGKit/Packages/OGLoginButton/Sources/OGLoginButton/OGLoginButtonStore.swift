import Combine
import NotificationCenter
import OGCore
import OGDIService
import OGDomainStore
import OGStorage

public typealias OGLoginButtonStore = OGDomainStore<OGLoginButtonState, OGLoginButtonAction>

extension OGDomainStore where State == OGLoginButtonState, Action == OGLoginButtonAction {
  public static func make() -> OGLoginButtonStore {
    OGLoginButtonStore(
      reducer: OGLoginButtonState.Reducer.reduce,
      connector: OGLoginButtonState.Connector()
    )
  }
}

// MARK: - LoginNavigationTitleButton

public struct LoginNavigationTitleButton: Equatable, Sendable {
  public let isEnabled: Bool
  public let enabledUrls: [String]
}

// MARK: - OGLoginButtonAction

public enum OGLoginButtonAction: OGDomainAction, Equatable {
  case _updatedNavigationTitleConfig(LoginNavigationTitleButton)
}

// MARK: - OGLoginButtonState

public struct OGLoginButtonState: OGDomainState, Equatable {
  public private(set) var isAwaitingUpdate: Bool
  public private(set) var isNavigationTitleButtonEnabled: Bool
  public private(set) var navigationTitleButton: LoginNavigationTitleButton?

  public init(
    isAwaitingUpdate: Bool = false,
    isNavigationTitleButtonEnabled: Bool = false,
    navigationTitleButton: LoginNavigationTitleButton? = nil
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.isNavigationTitleButtonEnabled = isNavigationTitleButtonEnabled
    self.navigationTitleButton = navigationTitleButton
  }

  mutating func update(
    navigationTitleButton: LoginNavigationTitleButton? = nil
  ) {
    self.navigationTitleButton = navigationTitleButton ?? self.navigationTitleButton
    isNavigationTitleButtonEnabled = self.navigationTitleButton?.isEnabled ?? false
  }

  public static let initial: Self = .init()
}

// MARK: OGLoginButtonState.Reducer

extension OGLoginButtonState {
  enum Reducer: OGDomainActionReducible {
    static func reduce(
      _ state: inout OGLoginButtonState,
      with action: OGLoginButtonAction
    ) {
      switch action {
      case let ._updatedNavigationTitleConfig(navigationTitleButton):
        state.update(navigationTitleButton: navigationTitleButton)
      }
    }
  }
}

// MARK: OGLoginButtonState.Connector

extension OGLoginButtonState {
  actor Connector: OGDomainConnector {
    private var cancelationTokens = Set<AnyCancellable>()
    private let loginButtonFeatureAdapter: LoginButtonFeatureAdaptable

    init(
      loginButtonFeatureAdapter: LoginButtonFeatureAdaptable = LoginButtonFeatureAdapterContainer.shared.loginButton()
    ) {
      self.loginButtonFeatureAdapter = loginButtonFeatureAdapter
    }

    func configure(
      dispatch: @escaping (OGLoginButtonAction) async -> Void
    ) async {
      loginButtonFeatureAdapter
        .configuration
        .compactMap {
          LoginNavigationTitleButton(
            isEnabled: $0.isEnabled,
            enabledUrls: $0.navigationTitle.enabledUrls
          )
        }
        .sink { navigationTitleButton in
          Task {
            await dispatch(._updatedNavigationTitleConfig(navigationTitleButton))
          }
        }
        .store(in: &cancelationTokens)
    }
  }
}
