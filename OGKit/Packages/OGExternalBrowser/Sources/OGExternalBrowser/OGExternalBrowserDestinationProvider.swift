import Foundation
import OGCore
import OGDIService
import OGRouter
import SwiftUI
import UIKit

// MARK: - OGExternalBrowserDestinationProvider

public struct OGExternalBrowserDestinationProvider: OGDestinationProvidable {
  @OGInjected(\OGRoutingContainer.routePublisher) private var router

  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.externalBrowser
  }

  public func provide(_ route: OGRoute) -> some View {
    if UIApplication.shared.canOpenURL(route.url) {
      UIApplication.shared.open(route.url)
      router.dismiss()
    }
    return EmptyView()
  }

  public func presentationType() -> OGPresentationType {
    .overlay
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}
