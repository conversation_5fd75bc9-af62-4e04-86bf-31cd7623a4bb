import AirshipCore
import Foundation
import NotificationCenter

// MARK: - OGAirshipPushCreatable

public protocol OGAirshipPushCreatable {
  func setupPush(with delegate: OGAirshipPushServing)
}

// MARK: - OGAirshipPushSetup

public final class OGAirshipPushSetup: OGAirshipPushCreatable {
  private let pushService: () -> OGAirshipPushServicing
  public init(pushService: @escaping () -> OGAirshipPushServicing = { Airship.push }) {
    self.pushService = pushService
    observeDidBecomeActive()
  }

  public func setupPush(with delegate: OGAirshipPushServing) {
    var push = pushService()
    push.defaultPresentationOptions = [.banner, .badge, .sound]
    push.notificationOptions = [.provisional, .alert, .badge, .sound, .carPlay]
    push.userPushNotificationsEnabled = true
    push.autobadgeEnabled = true
    push.pushNotificationDelegate = delegate
    push.registrationDelegate = delegate
    push.updateRegistration()
    push.resetBadge()
  }

  private func observeDidBecomeActive() {
    NotificationCenter.default.addObserver(
      self,
      selector: #selector(didBecomeActive),
      name: UIApplication.didBecomeActiveNotification,
      object: nil
    )
  }

  @MainActor @objc
  private func didBecomeActive() {
    pushService().resetBadge()
  }
}

// MARK: - OGAirshipPushServicing

public protocol OGAirshipPushServicing {
  var defaultPresentationOptions: UNNotificationPresentationOptions { get set }
  var notificationOptions: UANotificationOptions { get set }
  var userPushNotificationsEnabled: Bool { get set }
  var autobadgeEnabled: Bool { get set }
  var pushNotificationDelegate: PushNotificationDelegate? { get set }
  var registrationDelegate: RegistrationDelegate? { get set }
  func updateRegistration()
  func resetBadge()
}

// MARK: - AirshipPush + OGAirshipPushServicing

extension AirshipPush: OGAirshipPushServicing {}
