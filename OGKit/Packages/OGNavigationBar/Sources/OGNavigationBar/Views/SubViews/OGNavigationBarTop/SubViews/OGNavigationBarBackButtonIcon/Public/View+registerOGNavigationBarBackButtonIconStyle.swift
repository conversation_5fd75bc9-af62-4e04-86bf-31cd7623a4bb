import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGNavigationBarBackButtonIconStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGNavigationBarBackButtonIcon instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGNavigationBarBackButtonIconStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGNavigationBarBackButtonIconStyle``.
  /// - Returns: A view that represents a OGNavigationBarBackButtonIcon.
  @ViewBuilder
  public func register(_ style: some OGNavigationBarBackButtonIconStyle) -> some View {
    environment(\.styleOGNavigationBarBackButtonIcon, AnyOGNavigationBarBackButtonIconStyle(style: style))
  }
}
