import Combine
import OGDomainStore

public typealias OGAirshipInboxStore = OGDomainStore<OGAirshipInboxState, OGAirshipInboxAction>

extension OGDomainStore where State == OGAirshipInboxState, Action == OGAirshipInboxAction {
  public static func make() -> OGAirshipInboxStore {
    OGAirshipInboxStore(
      reducer: OGAirshipInboxState.Reducer.reduce,
      middlewares: OGAirshipInboxState.Middleware(),
      connector: OGAirshipInboxState.Connector()
    )
  }
}

// MARK: - OGAirshipInboxState

public struct OGAirshipInboxState: OGDomainState, Equatable {
  public private(set) var isAwaitingUpdate: Bool
  public private(set) var messages: [OGAirshipInboxMessage]
  public private(set) var unreadCount: Int

  public init(
    isAwaitingUpdate: Bool = false,
    messages: [OGAirshipInboxMessage] = [],
    unreadCount: Int = 0
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.messages = messages
    self.unreadCount = unreadCount
  }

  mutating func set(
    isAwaitingUpdate: Bool,
    messages: [OGAirshipInboxMessage]? = nil,
    unreadCount: Int? = nil
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
    self.messages = messages ?? self.messages
    self.unreadCount = unreadCount ?? self.unreadCount
  }

  public static let initial: Self = .init()
}

// MARK: - OGAirshipInboxAction

public enum OGAirshipInboxAction: OGDomainAction, Equatable {
  case deleteMessage(with: String)
  case deleteMessages(with: [String])
  case markRead(messageId: String)
  case refreshMessages

  /// Private actions
  case _set(messages: [OGAirshipInboxMessage], unreadCount: Int)
}

// MARK: - Reducer & Midleware

extension OGAirshipInboxState {
  public enum Reducer: OGDomainActionReducible {
    public static func reduce(
      _ state: inout OGAirshipInboxState,
      with action: OGAirshipInboxAction
    ) {
      switch action {
      case .deleteMessage, .deleteMessages, .markRead, .refreshMessages: state.set(isAwaitingUpdate: true)

      case let ._set(messages, unreadCount):
        let sortedMessages = messages.sorted(by: { $0.date > $1.date })
        state.set(
          isAwaitingUpdate: false,
          messages: sortedMessages,
          unreadCount: unreadCount
        )
      }
    }
  }

  public struct Middleware: OGDomainMiddleware {
    private let inboxService: OGAirshipInboxServing?

    init(inboxService: OGAirshipInboxServing? = OGAirshipContainer.shared.inboxService()) {
      self.inboxService = inboxService
    }

    public func callAsFunction(
      action: OGAirshipInboxAction,
      for _: OGAirshipInboxState
    ) async throws
      -> OGAirshipInboxAction? {
      switch action {
      case let .deleteMessages(messageIds):
        await inboxService?.deleteMessages(with: messageIds)
        return nil

      case let .deleteMessage(messageId):
        await inboxService?.deleteMessage(with: messageId)
        return nil

      case let .markRead(messageId):
        await inboxService?.markRead(messageId: messageId)
        return nil

      case .refreshMessages:
        _ = await inboxService?.refreshMessages()
        return nil

      case ._set:
        return nil
      }
    }
  }

  actor Connector: OGDomainConnector {
    private var cancellables = Set<AnyCancellable>()
    private let inboxService: OGAirshipInboxServing?

    init(
      inboxService: OGAirshipInboxServing? = OGAirshipContainer.shared.inboxService()
    ) {
      self.inboxService = inboxService
    }

    func configure(dispatch: @escaping (OGAirshipInboxAction) async -> Void) async {
      var messagePublisher: AnyPublisher<[OGAirshipInboxMessage], Never> {
        inboxService?
          .watchMessages() ?? Just([OGAirshipInboxMessage]()).eraseToAnyPublisher()
      }

      var unreadCountPublisher: AnyPublisher<Int, Never> {
        inboxService?
          .watchUnreadCount() ?? Just(0).eraseToAnyPublisher()
      }

      Publishers.CombineLatest(
        messagePublisher
          .removeDuplicates(),
        unreadCountPublisher
          .removeDuplicates()
      ).sink { messages, unreadCount in
        Task {
          await dispatch(._set(messages: messages, unreadCount: unreadCount))
        }
      }
      .store(in: &cancellables)
    }
  }
}
