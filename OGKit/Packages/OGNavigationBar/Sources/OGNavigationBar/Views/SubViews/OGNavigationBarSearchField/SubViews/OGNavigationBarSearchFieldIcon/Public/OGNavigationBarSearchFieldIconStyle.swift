import SwiftUI

/// A type that applies custom appearance to
/// all OGNavigationBarSearchFieldIcons within a view hierarchy.
public protocol OGNavigationBarSearchFieldIconStyle {
  /// A view that represents the body of a OGNavigationBarSearchFieldIcon.
  associatedtype Body: View

  /// The properties of a OGNavigationBarSearchFieldIcon.
  typealias Configuration = OGNavigationBarSearchFieldIconStyleConfiguration

  /// Creates a view that represents the body of a OGNavigationBarSearchFieldIcon.
  ///
  /// The system calls this method for each OGNavigationBarSearchFieldIcon instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGNavigationBarSearchFieldIconStyle``.
  /// ```swift
  /// struct MyCustomOGNavigationBarSearchFieldIconStyle: OGNavigationBarSearchFieldIconStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGNavigationBarSearchFieldIconConfiguration`` of the  OGNavigationBarSearchFieldIcon.
  /// - Returns: A view that represents a OGNavigationBarSearchFieldIcon.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
