// swiftlint:disable type_name

import SwiftUI

struct AnyOGNavigationBarSearchFieldStyle: OGNavigationBarSearchFieldStyle {
  private let _makeBody: (Configuration) -> AnyView

  init(style: some OGNavigationBarSearchFieldStyle) {
    self._makeBody = { configuration in
      AnyView(style.makeBody(configuration: configuration))
    }
  }

  func makeBody(configuration: Configuration) -> some View {
    _makeBody(configuration)
  }
}
