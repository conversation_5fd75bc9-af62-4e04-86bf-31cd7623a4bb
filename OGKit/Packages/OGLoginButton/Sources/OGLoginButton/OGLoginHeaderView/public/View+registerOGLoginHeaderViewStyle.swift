import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGLoginHeaderViewStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGLoginHeaderView instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGLoginHeaderViewStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGLoginHeaderViewStyle``.
  /// - Returns: A view that represents a OGLoginHeaderView.
  @ViewBuilder
  public func register(_ style: some OGLoginHeaderViewStyle) -> some View {
    environment(\.styleOGLoginHeaderView, AnyOGLoginHeaderViewStyle(style: style))
  }
}
