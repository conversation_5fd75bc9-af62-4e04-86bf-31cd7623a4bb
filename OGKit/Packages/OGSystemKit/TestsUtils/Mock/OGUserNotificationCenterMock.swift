import OGCore
import OGSystemKit
import UserNotifications

@OGMock
public final class OGUserNotificationCenterMock: OGUserNotificationCenterHandling {
  public init() {}

  public func authorizationStatus() async -> UNAuthorizationStatus {
    await mock.authorizationStatus()
  }

  public func requestAuthorization(options: UNAuthorizationOptions) async -> Bool {
    await mock.requestAuthorization(options: options)
  }
}
