// swiftlint:disable type_name

import SwiftUI

// MARK: - OGAirshipInboxIcon

struct OGAirshipInboxIcon: View {
  @Environment(\.styleOGAirshipInboxIcon) private var style

  init() {}

  var body: some View {
    style.makeBody(
      configuration: OGAirshipInboxIconStyleConfiguration(
        content: OGAirshipInboxIconStyleConfiguration.Content(
          content: content
        )
      )
    )
    .accessibilityIdentifier(AccessibilityIdentifiers.openInbox)
  }

  private var content: some View {
    Image("icon24x24Inbox", bundle: .main)
  }
}

// MARK: OGAirshipInboxIcon.AccessibilityIdentifiers

extension OGAirshipInboxIcon {
  enum AccessibilityIdentifiers {
    static let openInbox: String = "openInbox"
  }
}

// MARK: - OGAirshipInboxIcon_Previews

struct OGAirshipInboxIcon_Previews: PreviewProvider {
  static var previews: some View {
    OGAirshipInboxIcon()
  }
}
