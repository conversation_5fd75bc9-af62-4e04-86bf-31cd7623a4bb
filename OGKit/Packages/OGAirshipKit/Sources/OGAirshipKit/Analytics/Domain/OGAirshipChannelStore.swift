import Combine
import OGDomainStore

public typealias OGAirshipChannelStore = OGDomainStore<OGAirshipChannelState, OGAirshipChannelAction>

extension OGDomainStore where State == OGAirshipChannelState, Action == OGAirshipChannelAction {
  public static func make() -> OGAirshipChannelStore {
    OGAirshipChannelStore(
      reducer: OGAirshipChannelState.Reducer.reduce,
      middlewares: OGAirshipChannelState.Middleware()
    )
  }
}

// MARK: - OGAirshipChannelState

public struct OGAirshipChannelState: OGDomainState, Equatable {
  public var isAwaitingUpdate: Bool = false

  public static let initial: Self = .init()
}

// MARK: - OGAirshipChannelAction

public enum OGAirshipChannelAction: OGDomainAction, Equatable {
  case logIn
  case logOut
}

// MARK: - Reducer & Midleware

extension OGAirshipChannelState {
  public enum Reducer: OGDomainActionReducible {
    public static func reduce(
      _: inout OGAirshipChannelState,
      with action: OGAirshipChannelAction
    ) {
      switch action {
      case .logIn, .logOut:
        break
      }
    }
  }

  public struct Middleware: OGDomainMiddleware {
    private let channelService: OGAirshipChannelServing?

    init(
      channelService: OGAirshipChannelServing? = OGAirshipContainer.shared.channelService()
    ) {
      self.channelService = channelService
    }

    public func callAsFunction(
      action: OGAirshipChannelAction,
      for _: OGAirshipChannelState
    ) async throws
      -> OGAirshipChannelAction? {
      switch action {
      case .logOut:
        channelService?.setLoggedInTag(false)
        return nil
      case .logIn:
        channelService?.setLoggedInTag(true)
        return nil
      }
    }
  }
}
