import XCTest

@testable import OGLoginButton

final class OGLoginButtonStateReducerTests: XCTestCase {
  func test_WHEN_updatedNavigationTitleConfig_THEN_setNavigationTitleButton() throws {
    var state = OGLoginButtonState()
    let loginNavigationTitleButton = LoginNavigationTitleButton(
      isEnabled: true,
      enabledUrls: ["someURl"]
    )
    OGLoginButtonState.Reducer.reduce(&state, with: ._updatedNavigationTitleConfig(loginNavigationTitleButton))
    let expected = OGLoginButtonState(
      isNavigationTitleButtonEnabled: true,
      navigationTitleButton: loginNavigationTitleButton
    )
    XCTAssertEqual(state, expected)
  }
}
