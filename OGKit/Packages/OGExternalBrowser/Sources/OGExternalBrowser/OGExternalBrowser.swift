import Combine
import Foundation
import OGDIService
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import <PERSON>GMacros

// MARK: - OGExternalBrowserFeatureConfigurable

public protocol OGExternalBrowserFeatureConfigurable {
  var isEnabled: Bool { get set }
  var supportedUrls: [String] { get set }
}

// MARK: - OGExternalBrowserFeatureAdapter

public final class OGExternalBrowserFeatureAdapter: OGFeatureAdapter, OGExternalBrowserFeatureAdaptable {
  override public class var featureName: OGFeature.Name { OGIdentifier.externalBrowser.value }

  public let configuration: CurrentValueSubject<OGExternalBrowserFeatureConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()

  public init(configuration: OGExternalBrowserFeatureConfigurable?) {
    guard let configuration else {
      fatalError("The OGExternalBrowserFeatureConfiguration has not been registered")
    }
    self.configuration = CurrentValueSubject(configuration)
    super.init()

    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature.sink { [weak self] feature in
      guard let self else { return }
      var updatedConfiguration = self.configuration.value
      guard let feature else {
        updatedConfiguration.isEnabled = false
        self.configuration.send(updatedConfiguration)
        return
      }
      updatedConfiguration.isEnabled = feature.isEnabled

      let supportedUrls: [String] = feature.customValue(for: OGFeatureKey.CustomValues.ExternalBrowser.supportedUrls)
      updatedConfiguration.supportedUrls = supportedUrls
      self.configuration.send(updatedConfiguration)
    }.store(in: &subscriptions)
  }
}

// MARK: - OGExternalBrowserFeatureAdaptable

public protocol OGExternalBrowserFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<OGExternalBrowserFeatureConfigurable, Never> { get }
}

// MARK: - OGFeatureKey.CustomValues.ExternalBrowser

extension OGFeatureKey.CustomValues {
  public enum ExternalBrowser: String, OGKeyReceivable {
    public var value: String {
      rawValue
    }

    case supportedUrls
  }
}

extension OGIdentifier {
  public static let externalBrowser = #identifier("externalBrowser")
}
