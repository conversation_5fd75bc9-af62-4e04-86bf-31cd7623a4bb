// swift-tools-version: 5.7
import PackageDescription

let package = Package(
  name: "OGExternalBrowser",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    // Products define the executables and libraries a package produces, making them visible to other packages.
    .library(
      name: "OGExternalBrowser",
      targets: ["OGExternalBrowser"]
    )
  ],
  dependencies: [
    .package(
      path: "../OGCore"
    ),
    .package(
      path: "../OGRouter"
    ),
    .package(
      path: "../OGFeatureKit/Packages/OGFeatureAdapter"
    ),
    .package(
      path: "../OGExternalDependencies/OGDIService"
    )
  ],
  targets: [
    // Targets are the basic building blocks of a package, defining a module or a test suite.
    // Targets can depend on other targets in this package and products from dependencies.
    .target(
      name: "OGExternalBrowser",
      dependencies: [
        "OGCore",
        "OGRouter",
        "OGDIService",
        "OGFeatureAdapter"
      ]
    ),
    .testTarget(
      name: "OGExternalBrowserTests",
      dependencies: ["OGExternalBrowser"]
    )
  ]
)
