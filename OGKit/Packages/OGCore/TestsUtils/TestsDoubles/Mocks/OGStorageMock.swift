import Foundation
import OGCore
import OGMacros
import OGMock

// MARK: - OGStorageMock

@OGMock
public final class OGStorageMock: AnyPersistable {
  public init() {}
  
  public func persist(value: Any, forKey key: RawValueable) {
    mock.persist(value: value, forKey: key)
  }

  public func value(forKey key: RawValueable) -> Any? {
    mock.value(forKey: key)
  }

  public func delete(valueForKey key: RawValueable) {
    mock.delete(valueForKey: key)
  }

  public func bool(forKey key: RawValueable) -> Bool {
    mock.bool(forKey: key)
  }

  public func persistBool(_ value: Bool, forKey key: RawValueable) {
    mock.persistBool(value, forKey: key)
  }
}
