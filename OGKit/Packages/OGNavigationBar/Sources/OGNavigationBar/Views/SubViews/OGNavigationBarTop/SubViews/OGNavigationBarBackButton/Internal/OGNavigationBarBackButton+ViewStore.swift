import Combine
import Foundation
import OGDIService
import OGNavigationCore
import OGViewStore

// MARK: - OGNavigationBarBackButton.Store

extension OGNavigationBarBackButton {
  typealias Store = OGViewStore<ViewState, Event>
}

extension OGNavigationBarBackButton {
  @MainActor
  static func make() -> Store {
    OGNavigationBarBackButton.Store(
      reducer: Reducer.reduce,
      connector: Connector()
    )
  }
}

// MARK: - OGNavigationBarBackButton.Event

extension OGNavigationBarBackButton {
  enum Event: OGViewEvent {
    case _count(Int)
  }
}

// MARK: - OGNavigationBarBackButton.ViewState

extension OGNavigationBarBackButton {
  struct ViewState: OGViewState {
    private(set) var canGoBack: Bool = false
    private var count: Int?
    static var initial: OGNavigationBarBackButton.ViewState = .init()
    mutating func update(count: Int) {
      if self.count == nil {
        canGoBack = count > 0
        self.count = count
      }
    }
  }
}

extension OGNavigationBarBackButton {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _ state: inout ViewState,
      with event: Event
    ) {
      switch event {
      case let ._count(count):
        state.update(count: count)
      }
    }
  }

  actor Connector: OGViewStoreConnector {
    private let stackCount: OGNavigationStackCounting
    private var cancellables = Set<AnyCancellable>()

    init(stackCount: OGNavigationStackCounting = OGNavigationCoreContainer.shared.stackCount()) {
      self.stackCount = stackCount
    }

    func configure(
      dispatch: @escaping (Event) async -> Void
    ) async {
      stackCount
        .countPublisher
        .sink { value in
          Task {
            await dispatch(._count(value))
          }
        }
        .store(in: &cancellables)
    }
  }
}
