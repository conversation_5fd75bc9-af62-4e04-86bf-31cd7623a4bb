import Foundation

// MARK: - RequestObserverProtocol

protocol RequestObserverProtocol {
  func newRequestArrived(_ request: OGNetworkLoggerRequestModel)
}

// MARK: - RequestObserver

final class RequestObserver: RequestObserverProtocol {
  let options: [RequestObserverProtocol]

  init(options: [RequestObserverProtocol]) {
    self.options = options
  }

  func newRequestArrived(_ request: OGNetworkLoggerRequestModel) {
    options.forEach {
      $0.newRequestArrived(request)
    }
  }
}
