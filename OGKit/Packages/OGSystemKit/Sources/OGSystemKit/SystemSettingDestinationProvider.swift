import OGDIService
import OGMacros
import OGNavigation
import Swift<PERSON>

// MARK: - SystemSettingDestinationProvider

public struct SystemSettingDestinationProvider: OGDestinationProvidable {
  @OGInjected(\OGRoutingContainer.routePublisher) private var router

  public private(set) var identifier: OGIdentifier
  public init() {
    self.identifier = OGIdentifier.systemSetting
  }

  public func provide(_: OGRoute) -> some View {
    Task {
      await MainActor.run {
        if let url = URL(string: UIApplication.openSettingsURLString) {
          UIApplication.shared.open(url)
        }
      }
    }
    router.send(.dismiss)
    return EmptyView()
  }

  public func presentationType() -> OGPresentationType {
    .overlay
  }

  public func title(for _: OGRoute?) -> String? {
    nil
  }
}

extension OGRoute {
  public static let systemSetting = OGRoute(OGIdentifier.systemSetting.value)
}

extension OGIdentifier {
  public static let systemSetting = #identifier("systemSetting")
}
