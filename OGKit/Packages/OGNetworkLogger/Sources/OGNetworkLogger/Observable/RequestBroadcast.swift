import Foundation

// MARK: - RequestBroadcastDelegate

public protocol RequestBroadcastDelegate: AnyObject {
  func newRequestArrived(_ request: OGNetworkLoggerRequestModel)
}

// MARK: - RequestBroadcast

public final class RequestBroadcast: RequestObserverProtocol {
  public static let shared = RequestBroadcast()

  var delegate = ThreadSafe<RequestBroadcastDelegate?>(nil)

  private init() {}

  public func setDelegate(_ newDelegate: RequestBroadcastDelegate) {
    delegate.atomically { delegate in
      delegate = newDelegate
    }
  }

  public func removeDelegate() {
    delegate.atomically { delegate in
      delegate = nil
    }
  }

  func newRequestArrived(_ request: OGNetworkLoggerRequestModel) {
    delegate.atomically { delegate in
      delegate?.newRequestArrived(request)
    }
  }
}
