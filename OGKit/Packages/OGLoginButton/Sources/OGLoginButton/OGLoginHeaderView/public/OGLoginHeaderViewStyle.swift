import SwiftUI

/// A type that applies custom appearance to
/// all OGLoginHeaderViews within a view hierarchy.
public protocol OGLoginHeaderViewStyle {
  /// A view that represents the body of a OGLoginHeaderView.
  associatedtype Body: View

  /// The properties of a OGLoginHeaderView.
  typealias Configuration = OGLoginHeaderViewStyleConfiguration

  /// Creates a view that represents the body of a OGLoginHeaderView.
  ///
  /// The system calls this method for each OGLoginHeaderView instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGLoginHeaderViewStyle``.
  /// ```swift
  /// struct MyCustomOGLoginHeaderViewStyle: OGLoginHeaderViewStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGLoginHeaderViewConfiguration`` of the  OGLoginHeaderView.
  /// - Returns: A view that represents a OGLoginHeaderView.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
