import Foundation

// MARK: - Ignore

public enum Ignore {
  case disbaled
  case enabled(ignoreHandler: (OGNetworkLoggerRequestModel) -> Bool)
}

// MARK: - IgnoreManagerable

public protocol IgnoreManagerable {
  var ignore: Ignore { get set }
  func setIgnore(_ ignore: Ignore)
}

// MARK: - IgnoreManager

public final class IgnoreManager: IgnoreManagerable {
  public var ignore: Ignore = .disbaled

  public init() {}

  public func setIgnore(_ ignore: Ignore) {
    self.ignore = ignore
  }
}
