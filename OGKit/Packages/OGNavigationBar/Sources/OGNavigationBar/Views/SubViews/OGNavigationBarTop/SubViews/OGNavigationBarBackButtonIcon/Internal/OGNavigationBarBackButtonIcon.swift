// swiftlint:disable type_name

import SwiftUI

// MARK: - OGNavigationBarBackButtonIcon

struct OGNavigationBarBackButtonIcon: View {
  @Environment(\.styleOGNavigationBarBackButtonIcon) private var style

  init() {}

  var body: some View {
    style.makeBody(
      configuration: OGNavigationBarBackButtonIconStyleConfiguration(
        content: OGNavigationBarBackButtonIconStyleConfiguration.Content(
          content: content
        )
      )
    )
  }

  private var content: some View {
    Image(systemName: "chevron.left")
  }
}

// MARK: - OGNavigationBarBackButtonIcon_Previews

struct OGNavigationBarBackButtonIcon_Previews: PreviewProvider {
  static var previews: some View {
    OGNavigationBarBackButtonIcon()
  }
}
