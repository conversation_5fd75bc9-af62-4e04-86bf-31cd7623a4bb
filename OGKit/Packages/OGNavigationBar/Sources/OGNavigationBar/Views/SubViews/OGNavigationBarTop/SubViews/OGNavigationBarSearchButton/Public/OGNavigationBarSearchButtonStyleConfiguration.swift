// swiftlint:disable type_name

import SwiftUI

/// The properties of a OGNavigationBarSearchButton.
public struct OGNavigationBarSearchButtonStyleConfiguration {
  /// A view that describes the content of a the OGNavigationBarSearchButton.
  public let content: OGNavigationBarSearchButtonStyleConfiguration.Content

  /// The type-erased content of a OGNavigationBarSearchButton.
  public struct Content: View {
    init(content: some View) {
      self.body = AnyView(content)
    }

    /// The type of view representing the body of this view.
    public var body: AnyView
  }
}
