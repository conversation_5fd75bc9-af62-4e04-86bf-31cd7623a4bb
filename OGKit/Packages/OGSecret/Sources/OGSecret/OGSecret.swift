import Foundation
import OGCore

// MARK: - OGSecret

public struct OGSecret: <PERSON><PERSON><PERSON>, OGIdentifierProvidable {
  public let identifier: OGIdentifier
  public let secret: String
  public init(identifier: OGIdentifier, secret: String) {
    self.identifier = identifier
    self.secret = secret
  }

  public func hash(into hasher: inout Hasher) {
    hasher.combine(identifier.value)
  }
}

// MARK: OGSecret.Name

extension OGSecret {
  public typealias Name = String
}

// MARK: - OGSecret.Name + OGIdentifierProvidable

extension OGSecret.Name: OGIdentifierProvidable {
  public var identifier: OGIdentifier {
    try! OGIdentifier(self)
  }
}
