// swiftlint:disable type_name

import OGCore
import OGDIService
import <PERSON><PERSON>outer
import OGSearch
import SwiftUI

// MARK: - OGNavigationBarSearchField

struct OGNavigationBarSearchField: View {
  @Environment(\.styleOGNavigationBarSearchField) private var style
  @Binding var state: OGNavigationBarScrollViewState
  @StateObject private var viewStore: Store = Self.make()
  var body: some View {
    style.makeBody(
      configuration: OGNavigationBarSearchFieldStyleConfiguration(
        content: OGNavigationBarSearchFieldStyleConfiguration.Content(
          content: content
        )
      )
    )
    .accessibilityIdentifier(AccessibilityIdentifiers.searchBar)
    .onTapGesture {
      Task {
        await viewStore.dispatch(.didTap)
      }
    }
  }

  private var content: some View {
    HStack(spacing: .zero) {
      OGNavigationBarSearchFieldIcon()
        .opacity(state.progressFast)
      OGNavigationBarSearchFieldText()
        .opacity(state.progressFast)
      Spacer()
    }
    .frame(maxWidth: .infinity)
    .frame(height: state.searchFieldHeight)
  }
}

// MARK: OGNavigationBarSearchField.AccessibilityIdentifiers

extension OGNavigationBarSearchField {
  enum AccessibilityIdentifiers {
    static let searchBar: String = "searchBar"
  }
}
