import Combine
import Foundation
import OGCore
import OGDIService
import OGFeatureAdapter
import OGFeatureCore
import OGIdentifier
import <PERSON>GMacros

// MARK: - SearchFeatureConfigurable

public protocol SalutationFeatureConfigurable {
  var isEnabled: Bool { get set }
  var salutationUrls: [String] { get set }
}

// MARK: - SearchFeatureAdapter

public final class SalutationFeatureAdapter: OGFeatureAdapter, SalutationFeatureAdaptable {
  
  override public class var featureName: OGFeature.Name { OGIdentifier.salutation.value }

  public let configuration: CurrentValueSubject<SalutationFeatureConfigurable, Never>
  private var subscriptions = Set<AnyCancellable>()

  public init(configuration: SalutationFeatureConfigurable?) {
    guard let configuration else {
      fatalError("The SalutationFeatureConfiguration has not been registered")
    }
    self.configuration = CurrentValueSubject(configuration)
    super.init()

    receiveUpdates()
  }

  private func receiveUpdates() {
    $feature.sink { [weak self] feature in
      guard let self else { return }
      var updatedConfiguration = self.configuration.value
      guard let feature else {
        updatedConfiguration.isEnabled = false
        self.configuration.send(updatedConfiguration)
        return
      }
      updatedConfiguration.isEnabled = feature.isEnabled
      
      let salutationUrls: [String] = feature.customValue(for: OGFeatureKey.CustomValues.Salutation.salutationUrls)
      updatedConfiguration.salutationUrls = salutationUrls
      
      self.configuration.send(updatedConfiguration)
    }.store(in: &subscriptions)
  }
}

// MARK: - SearchFeatureAdaptable

public protocol SalutationFeatureAdaptable: OGFeatureAdaptable {
  var configuration: CurrentValueSubject<SalutationFeatureConfigurable, Never> { get }
}

// MARK: - OGFeatureKey.CustomValues.Search

extension OGFeatureKey.CustomValues {
  public enum Salutation: String, OGKeyReceivable {
    public var value: String {
      rawValue
    }

    case salutationUrls
  }
}

extension OGIdentifier {
  public static let salutation = #identifier("salutation")
}
