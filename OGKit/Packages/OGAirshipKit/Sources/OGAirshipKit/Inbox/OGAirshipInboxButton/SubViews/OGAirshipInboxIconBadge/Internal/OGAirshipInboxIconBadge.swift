// swiftlint:disable type_name

import SwiftUI

// MARK: - OGAirshipInboxIconBadge

struct OGAirshipInboxIconBadge: View {
  @Environment(\.styleOGAirshipInboxIconBadge) var style
  let unreadCount: Int

  var body: some View {
    style.makeBody(configuration: OGAirshipInboxIconBadgeStyleConfiguration(
      content: OGAirshipInboxIconBadgeStyleConfiguration.Content(content: content)))
  }

  private var content: some View {
    Text("\(unreadCount)")
  }
}

// MARK: - OGAirshipInboxIconBadge_Previews

struct OGAirshipInboxIconBadge_Previews: PreviewProvider {
  static var previews: some View {
    OGAirshipInboxIconBadge(unreadCount: 1)
  }
}
