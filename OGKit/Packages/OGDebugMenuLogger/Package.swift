// swift-tools-version: 5.7
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
  name: "OGDebugMenuLogger",
  platforms: [
    .iOS(.v15)
  ],
  products: [
    .library(
      name: "OGDebugMenuLogger",
      targets: ["OGDebugMenuLogger"]
    )
  ],
  dependencies: [
    .package(path: "../OGCore"),
    .package(path: "../OGExternalDependencies/OGDIService")
  ],
  targets: [
    .target(
      name: "OGDebugMenuLogger",
      dependencies: [
        "OGCore",
        "OGDIService"
      ]
    )
  ]
)
