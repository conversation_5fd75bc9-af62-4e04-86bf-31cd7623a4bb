import Foundation
import OGDIService

public final class OGNetworkLoggerContainer: OGDISharedContainer {
  public static var shared: OGNetworkLoggerContainer = .init()
  public var manager: OGDIContainerManager = .init()

  public var networkLogger: OGDIService<OGNetworkLoggerable> {
    self {
      OGNetworkLogger()
    }.cached
  }

  internal var networkLoggerConfig: OGDIService<NetworkLoggerConfigable> {
    self {
      NetworkLoggerConfig()
    }.cached
  }

  public var ignoreManager: OGDIService<IgnoreManagerable> {
    self {
      IgnoreManager()
    }.cached
  }

  public var networkLoggerStorage: OGDIService<OGNetworkLoggerStorageable> {
    self {
      OGNetworkLoggerStorage()
    }.cached
  }
}
