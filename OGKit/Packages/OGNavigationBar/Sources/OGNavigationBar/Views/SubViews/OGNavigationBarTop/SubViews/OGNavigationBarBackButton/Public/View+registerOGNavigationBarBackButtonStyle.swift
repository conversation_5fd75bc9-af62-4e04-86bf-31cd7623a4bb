import SwiftUI

extension View {
  /// Sets the style within this view to a ``UICatalog/OGNavigationBarBackButtonStyle`` with a
  /// custom appearance.
  ///
  /// Use this modifier to set a specific style for  OGNavigationBarBackButton instances
  /// within the view hierarchy.
  /// ```swift
  ///  var body: some Scene {
  ///		WindowGroup {
  /// 	MyView()
  ///		.register(MyCustomOGNavigationBarBackButtonStyle())
  ///  }
  /// ```
  /// - Parameter style : A struct conforming to  ``UICatalog/OGNavigationBarBackButtonStyle``.
  /// - Returns: A view that represents a OGNavigationBarBackButton.
  @ViewBuilder
  public func register(_ style: some OGNavigationBarBackButtonStyle) -> some View {
    environment(\.styleOGNavigationBarBackButton, AnyOGNavigationBarBackButtonStyle(style: style))
  }
}
