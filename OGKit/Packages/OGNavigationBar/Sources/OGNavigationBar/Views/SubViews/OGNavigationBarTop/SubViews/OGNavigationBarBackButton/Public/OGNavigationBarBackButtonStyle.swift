import SwiftUI

/// A type that applies custom appearance to
/// all OGNavigationBarBackButtons within a view hierarchy.
public protocol OGNavigationBarBackButtonStyle {
  /// A view that represents the body of a OGNavigationBarBackButton.
  associatedtype Body: View

  /// The properties of a OGNavigationBarBackButton.
  typealias Configuration = OGNavigationBarBackButtonStyleConfiguration

  /// Creates a view that represents the body of a OGNavigationBarBackButton.
  ///
  /// The system calls this method for each OGNavigationBarBackButton instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGNavigationBarBackButtonStyle``.
  /// ```swift
  /// struct MyCustomOGNavigationBarBackButtonStyle: OGNavigationBarBackButtonStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGNavigationBarBackButtonConfiguration`` of the  OGNavigationBarBackButton.
  /// - Returns: A view that represents a OGNavigationBarBackButton.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
