import OGCore
import OGDIService
import OGRouter
import OGViewStore

// MARK: - OGNavigationBarSearchButton.Store

extension OGNavigationBarSearchButton {
  typealias Store = OGViewStore<ViewState, Event>
}

extension OGNavigationBarSearchButton {
  @MainActor
  static func make() -> Store {
    OGNavigationBarSearchButton.Store(
      reducer: Reducer.reduce,
      middleware: Middleware()
    )
  }
}

// MARK: - OGNavigationBarSearchButton.Event

extension OGNavigationBarSearchButton {
  enum Event: OGViewEvent {
    case didTap
  }
}

// MARK: - OGNavigationBarSearchButton.ViewState

extension OGNavigationBarSearchButton {
  struct ViewState: OGViewState {
    static var initial: OGNavigationBarSearchButton.ViewState = .init()
  }
}

extension OGNavigationBarSearchButton {
  enum Reducer: OGViewEventReducible {
    static func reduce(
      _: inout ViewState,
      with _: Event
    ) {}
  }

  struct Middleware: OGViewStoreMiddleware {
    private let router: OGRoutePublishing

    init(router: OGRoutePublishing = OGRoutingContainer.shared.routePublisher()) {
      self.router = router
    }

    func callAsFunction(
      event: Event,
      for _: ViewState
    ) async
      -> Event? {
      switch event {
      case .didTap:
        router.send(OGRoute(OGIdentifier.search.value, data: OGSearchTapOrigin.button))
        return nil
      }
    }
  }
}
