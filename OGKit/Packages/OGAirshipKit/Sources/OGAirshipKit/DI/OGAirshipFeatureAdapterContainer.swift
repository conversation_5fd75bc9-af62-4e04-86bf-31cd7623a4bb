// Generated using OGFeatureAdapterMaker
import OGDIService

public final class OGAirshipFeatureAdapterContainer: OGDISharedContainer {
  public static var shared: OGAirshipFeatureAdapterContainer = .init()
  public var manager: OGDIContainerManager = .init()

  public var airship: OGDIService<AirshipFeatureAdaptable> {
    self {
      OGAirshipFeatureAdapter(configuration: nil)
    }.cached
  }

  public var airshipPush: OGDIService<AirshipPushFeatureAdaptable> {
    self {
      OGAirshipPushFeatureAdapter()
    }.cached
  }
}
