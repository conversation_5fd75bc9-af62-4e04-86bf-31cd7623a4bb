packages:
  Account:
    path: Packages/Account/
  Airship:
    from: 17.5.0
    url: https://github.com/urbanairship/ios-library
  AppCore:
    path: Packages/AppCore/
  AppTracker:
    path: Packages/AppTracker/
  AppUpdate:
    path: Packages/AppUpdate/
  Assortment:
    path: Packages/Assortment/
  BraFittingGuide:
    path: Packages/BraFittingGuide/
  CatalogScanner:
    path: Packages/CatalogScanner/
  Deals:
    path: Packages/Deals/
  ExternalDependencies:
    path: Packages/ExternalDependencies/
  Inbox:
    path: Packages/Inbox/
  Login:
    path: Packages/Login/
  NativeAPI:
    path: Packages/NativeAPI/
  OGAdjustReporter:
    path: OGKit/Packages/OGTracker/Packages/OGAdjustReporter/
  OGAirshipKit:
    path: OGKit/Packages/OGAirshipKit/
  OGAirshipReporter:
    path: OGKit/Packages/OGTracker/Packages/OGAirshipReporter/
  OGApp:
    path: Packages/OGApp/
  OGAppEnvironment:
    path: OGKit/Packages/OGCore/Packages/OGAppEnvironment/
  OGAppLifecycle:
    path: OGKit/Packages/OGAppLifecycle/
  OGAsyncImage:
    path: Packages/OGAsyncImage/
  OGBackports:
    path: OGKit/Packages/OGExternalDependencies/OGBackports/
  OGBadge:
    path: OGKit/Packages/OGBadge/
  OGBundledFeatureSetFetcher:
    path: OGKit/Packages/OGFeatureKit/Packages/OGBundledFeatureSetFetcher/
  OGCopyCodeBanner:
    path: OGKit/Packages/OGCopyCodeBanner/
  OGCore:
    path: OGKit/Packages/OGCore/
  OGDIService:
    path: OGKit/Packages/OGExternalDependencies/OGDIService/
  OGDebugMenuLogger:
    path: OGKit/Packages/OGDebugMenuLogger/
  OGDeepLinkHandler:
    path: OGKit/Packages/OGDeepLinkHandler/
  OGDetective:
    path: OGKit/Packages/OGDetective/
  OGDetectiveComponent:
    path: OGKit/Packages/OGDetectiveComponent/
  OGDialogCoordinator:
    path: OGKit/Packages/OGDialogCoordinator/
  OGDomainStore:
    path: OGKit/Packages/OGDomainStore/
  OGExternalBrowser:
    path: OGKit/Packages/OGExternalBrowser/
  OGExternalDependencies:
    path: OGKit/Packages/OGExternalDependencies/
  OGFeatureAdapter:
    path: OGKit/Packages/OGFeatureKit/Packages/OGFeatureAdapter/
  OGFeatureConfigView:
    path: OGKit/Packages/OGFeatureKit/Packages/OGFeatureConfigView/
  OGFeatureCore:
    path: OGKit/Packages/OGFeatureKit/Packages/OGFeatureCore/
  OGFeatureKit:
    path: OGKit/Packages/OGFeatureKit/
  OGFeatureManager:
    path: OGKit/Packages/OGFeatureKit/Packages/OGFeatureManager/
  OGFirebaseKit:
    path: OGKit/Packages/OGFirebaseKit/
  OGFirebaseReporter:
    path: OGKit/Packages/OGTracker/Packages/OGFirebaseReporter/
  OGHTTPClient:
    path: OGKit/Packages/OGHTTPClient/
  OGIdentifier:
    path: OGKit/Packages/OGCore/Packages/OGIdentifier/
  OGInAppBrowser:
    path: OGKit/Packages/OGInAppBrowser/
  OGL10n:
    path: OGKit/Packages/OGL10n/
  OGLogger:
    path: OGKit/Packages/OGCore/Packages/OGLogger/
  OGLoginButton:
    path: OGKit/Packages/OGLoginButton/
  OGMacros:
    path: OGKit/Packages/OGCore/Packages/OGMacros/
  OGMock:
    path: OGKit/Packages/OGCore/Packages/OGMock/
  OGNavigation:
    path: OGKit/Packages/OGNavigation/
  OGNavigationBar:
    path: OGKit/Packages/OGNavigationBar/
  OGNavigationCore:
    path: OGKit/Packages/OGNavigation/Packages/OGNavigationCore/
  OGNetworkLogger:
    path: OGKit/Packages/OGNetworkLogger/
  OGRemoteFeatureSetFetcher:
    path: OGKit/Packages/OGFeatureKit/Packages/OGRemoteFeatureSetFetcher/
  OGRouter:
    path: OGKit/Packages/OGRouter/
  OGSalutation:
    path: OGKit/Packages/OGSalutation/
  OGScreenViewUpdate:
    path: OGKit/Packages/OGDialogCoordinator/Packages/OGScreenViewUpdate/
  OGSearch:
    path: OGKit/Packages/OGSearch/
  OGSecret:
    path: OGKit/Packages/OGSecret/
  OGStorage:
    path: OGKit/Packages/OGCore/Packages/OGStorage/
  OGSwiftConcurrencyExtras:
    path: OGKit/Packages/OGExternalDependencies/OGSwiftConcurrencyExtras/
  OGSystemKit:
    path: OGKit/Packages/OGSystemKit/
  OGTenantCore:
    path: OGKit/Packages/OGFeatureKit/Packages/OGTenantCore/
  OGTenantKit:
    path: OGKit/Packages/OGFeatureKit/Packages/OGTenantKit/
  OGTenantSwitch:
    path: OGKit/Packages/OGFeatureKit/Packages/OGTenantSwitch/
  OGTestEnvironmentKit:
    path: OGKit/Packages/OGFeatureKit/Packages/OGTestEnvironmentKit/
  OGTracker:
    path: OGKit/Packages/OGTracker/
  OGTrackerCore:
    path: OGKit/Packages/OGTracker/Packages/OGTrackerCore/
  OGTrackerOptInService:
    path: OGKit/Packages/OGTracker/Packages/OGTrackerOptInService/
  OGURLCredentialStorage:
    path: OGKit/Packages/OGURLCredentialStorage/
  OGUserAgent:
    path: OGKit/Packages/OGUserAgent/
  OGUserCore:
    path: OGKit/Packages/OGUserCore/
  OGViewStore:
    path: OGKit/Packages/OGViewStore/
  OGWebBridge:
    path: OGKit/Packages/OGWebBridge/
  OGWebBridgeTracker:
    path: OGKit/Packages/OGTracker/Packages/OGWebBridgeTracker/
  OGWebView:
    path: OGKit/Packages/OGWebView/
  OGZipArchiver:
    path: OGKit/Packages/OGCore/Packages/OGZipArchiver/
  ProductDetail:
    path: Packages/ProductDetail/
  ProductReviewDetail:
    path: Packages/ProductReviewDetail/
  PromoBanner:
    path: Packages/PromoBanner/
  PushPromotion:
    path: Packages/PushPromotion/
  Recommendation:
    path: Packages/Recommendation/
  Review:
    path: Packages/Review/
  Salutation:
    path: Packages/Salutation/
  Search:
    path: Packages/Search/
  StoreFinder:
    path: Packages/StoreFinder/
  TabBar:
    path: Packages/TabBar/
  TenantChooser:
    path: Packages/TenantChooser/
  TenantChooserCore:
    path: Packages/TenantChooserCore/
  TenantSwitch:
    path: Packages/TenantSwitch/
  UICatalog:
    path: Packages/UICatalog/
  Welcome:
    path: Packages/Welcome/
