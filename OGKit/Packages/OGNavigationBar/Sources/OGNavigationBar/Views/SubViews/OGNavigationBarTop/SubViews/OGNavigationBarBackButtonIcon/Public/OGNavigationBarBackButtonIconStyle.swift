import SwiftUI

/// A type that applies custom appearance to
/// all OGNavigationBarBackButtonIcons within a view hierarchy.
public protocol OGNavigationBarBackButtonIconStyle {
  /// A view that represents the body of a OGNavigationBarBackButtonIcon.
  associatedtype Body: View

  /// The properties of a OGNavigationBarBackButtonIcon.
  typealias Configuration = OGNavigationBarBackButtonIconStyleConfiguration

  /// Creates a view that represents the body of a OGNavigationBarBackButtonIcon.
  ///
  /// The system calls this method for each OGNavigationBarBackButtonIcon instance in a view
  /// hierarchy where this style is the current ``UICatalog/OGNavigationBarBackButtonIconStyle``.
  /// ```swift
  /// struct MyCustomOGNavigationBarBackButtonIconStyle: OGNavigationBarBackButtonIconStyle {
  ///	func makeBody(configuration: Configuration) -> some View {
  ///		configuration.content
  ///		.background(.red)
  ///		.foregroundColor(.white)
  ///		.cornerRadius(6)
  ///		.font(.largeTitle)
  ///	}
  /// }
  /// ```
  /// - Parameter configuration : The ``UICatalog/OGNavigationBarBackButtonIconConfiguration`` of the  OGNavigationBarBackButtonIcon.
  /// - Returns: A view that represents a OGNavigationBarBackButtonIcon.
  @ViewBuilder
  func makeBody(configuration: Self.Configuration) -> Self.Body
}
