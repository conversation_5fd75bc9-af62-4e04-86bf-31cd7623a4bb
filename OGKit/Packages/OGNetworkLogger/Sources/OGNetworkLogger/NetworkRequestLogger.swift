import Foundation
import OGDIService

@objc
class NetworkRequestLogger: NSObject {
  @OGInjected(\OGNetworkLoggerContainer.networkLoggerConfig) private var networkLoggerConfig

  func swizzleProtocolClasses() {
    let instance = URLSessionConfiguration.default
    let uRLSessionConfigurationClass: AnyClass = object_getClass(instance)!

    let method1: Method = class_getInstanceMethod(uRLSessionConfigurationClass, #selector(getter: uRLSessionConfigurationClass.protocolClasses))!
    let method2: Method = class_getInstanceMethod(URLSessionConfiguration.self, #selector(URLSessionConfiguration.fakeProcotolClasses))!

    method_exchangeImplementations(method1, method2)
  }

  func startLogger() {
    networkLoggerConfig.loggerEnable = true
    URLProtocol.registerClass(NetworkLoggerUrlProtocol.self)
  }

  func stopLogger() {
    networkLoggerConfig.loggerEnable = false
    URLProtocol.unregisterClass(NetworkLoggerUrlProtocol.self)
  }
}
