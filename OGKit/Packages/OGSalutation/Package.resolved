{"pins": [{"identity": "abseil-cpp-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/abseil-cpp-binary.git", "state": {"revision": "748c7837511d0e6a507737353af268484e1745e2", "version": "1.2024011601.1"}}, {"identity": "app-check", "kind": "remoteSourceControl", "location": "https://github.com/google/app-check.git", "state": {"revision": "076b241a625e25eac22f8849be256dfb960fcdfe", "version": "10.19.1"}}, {"identity": "factory", "kind": "remoteSourceControl", "location": "https://github.com/hmlongco/Factory", "state": {"revision": "061b3afe0358a0da7ce568f8272c847910be3dd7", "version": "2.2.0"}}, {"identity": "firebase-ios-sdk", "kind": "remoteSourceControl", "location": "https://github.com/firebase/firebase-ios-sdk.git", "state": {"revision": "42eae77a0af79e9c3f41df04a23c76f05cfdda77", "version": "10.24.0"}}, {"identity": "googleappmeasurement", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleAppMeasurement.git", "state": {"revision": "51ba746a9d51a4bd0774b68499b0c73ef6e8570d", "version": "10.24.0"}}, {"identity": "googledatatransport", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleDataTransport.git", "state": {"revision": "a637d318ae7ae246b02d7305121275bc75ed5565", "version": "9.4.0"}}, {"identity": "googleutilities", "kind": "remoteSourceControl", "location": "https://github.com/google/GoogleUtilities.git", "state": {"revision": "57a1d307f42df690fdef2637f3e5b776da02aad6", "version": "7.13.3"}}, {"identity": "grpc-binary", "kind": "remoteSourceControl", "location": "https://github.com/google/grpc-binary.git", "state": {"revision": "e9fad491d0673bdda7063a0341fb6b47a30c5359", "version": "1.62.2"}}, {"identity": "gtm-session-fetcher", "kind": "remoteSourceControl", "location": "https://github.com/google/gtm-session-fetcher.git", "state": {"revision": "0382ca27f22fb3494cf657d8dc356dc282cd1193", "version": "3.4.1"}}, {"identity": "interop-ios-for-google-sdks", "kind": "remoteSourceControl", "location": "https://github.com/google/interop-ios-for-google-sdks.git", "state": {"revision": "2d12673670417654f08f5f90fdd62926dc3a2648", "version": "100.0.0"}}, {"identity": "ios-library", "kind": "remoteSourceControl", "location": "https://github.com/urbanairship/ios-library", "state": {"revision": "53040c77617a2acc5d9a7b69cf5bbbf36f94ad4b", "version": "17.7.3"}}, {"identity": "leveldb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/leveldb.git", "state": {"revision": "a0bc79961d7be727d258d33d5a6b2f1023270ba1", "version": "1.22.5"}}, {"identity": "nanopb", "kind": "remoteSourceControl", "location": "https://github.com/firebase/nanopb.git", "state": {"revision": "b7e1104502eca3a213b46303391ca4d3bc8ddec1", "version": "2.30910.0"}}, {"identity": "og-dx_aac-multiplatform-sdk", "kind": "remoteSourceControl", "location": "https://github.com/aacml/og-dx_aac-multiplatform-sdk.git", "state": {"revision": "0ca06182b35b9f0620d2f1b629aa52fea7c19ef4", "version": "2.2.1"}}, {"identity": "promises", "kind": "remoteSourceControl", "location": "https://github.com/google/promises.git", "state": {"revision": "540318ecedd63d883069ae7f1ed811a2df00b6ac", "version": "2.4.0"}}, {"identity": "snowplow-ios-tracker", "kind": "remoteSourceControl", "location": "https://github.com/snowplow/snowplow-ios-tracker", "state": {"revision": "20b1fea9c58334e569cb63d71875d3c2d0243483", "version": "6.0.7"}}, {"identity": "swift-async-algorithms", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-async-algorithms", "state": {"revision": "9cfed92b026c524674ed869a4ff2dcfdeedf8a2a", "version": "0.1.0"}}, {"identity": "swift-collections", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-collections.git", "state": {"revision": "ee97538f5b81ae89698fd95938896dec5217b148", "version": "1.1.1"}}, {"identity": "swift-concurrency-extras", "kind": "remoteSourceControl", "location": "https://github.com/pointfreeco/swift-concurrency-extras", "state": {"revision": "bb5059bde9022d69ac516803f4f227d8ac967f71", "version": "1.1.0"}}, {"identity": "swift-protobuf", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-protobuf.git", "state": {"revision": "9f0c76544701845ad98716f3f6a774a892152bcb", "version": "1.26.0"}}, {"identity": "swift-syntax", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-syntax.git", "state": {"revision": "6ad4ea24b01559dde0773e3d091f1b9e36175036", "version": "509.0.2"}}, {"identity": "ziparchive", "kind": "remoteSourceControl", "location": "https://github.com/ZipArchive/ZipArchive.git", "state": {"revision": "38e0ce0598e06b034271f296a8e15b149c91aa19", "version": "2.4.3"}}], "version": 2}