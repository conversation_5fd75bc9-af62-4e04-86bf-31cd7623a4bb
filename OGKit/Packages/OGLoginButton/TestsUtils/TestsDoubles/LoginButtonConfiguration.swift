import Foundation
import OGCore
import OGDIService
import OGLoginButton

// MARK: - LoginButtonFeatureConfiguration

struct LoginButtonFeatureConfiguration: LoginButtonFeatureConfigurable {
  var isEnabled: Bool = true
  var navigationTitle = LoginButtonConfiguration(enabledUrls: [])
}

// MARK: - LoginButtonFeatureAdapterContainer + AutoRegistering

extension LoginButtonFeatureAdapterContainer: AutoRegistering {
  public func autoRegister() {
    guard OGCoreContainer.shared.appEnvironment().isTestsBuild else { return }
    loginButton.register {
      LoginButtonFeatureAdapter(configuration: LoginButtonFeatureConfiguration())
    }
  }
}
